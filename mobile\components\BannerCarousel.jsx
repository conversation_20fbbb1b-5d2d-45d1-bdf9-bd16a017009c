import React, { useState, useEffect, useRef } from 'react';
import { View, ScrollView, Dimensions, TouchableOpacity } from 'react-native';
import { Image } from '@/components/ui/image';
import { Text } from '@/components/ui/text';
import { Box } from '@/components/ui/box';
import { COLORS } from '../constants/colors';

const { width: screenWidth } = Dimensions.get('window');

export const BannerCarousel = () => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const scrollViewRef = useRef(null);

  // Sample banner data - bu veriler backend'den gelecek
  const banners = [
    {
      id: 1,
      title: '<PERSON><PERSON>zon <PERSON>ndirimleri',
      subtitle: '%50\'ye varan indirimler',
      image: 'https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=800&h=400&fit=crop',
      backgroundColor: '#FF6B6B',
    },
    {
      id: 2,
      title: 'Elektronik Ürünlerde',
      subtitle: '<PERSON>zel fırsatlar sizi bekliyor',
      image: 'https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=800&h=400&fit=crop',
      backgroundColor: '#4ECDC4',
    },
    {
      id: 3,
      title: 'Açık Artırma',
      subtitle: 'Nadir bulunan ürünler',
      image: 'https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?w=800&h=400&fit=crop',
      backgroundColor: '#45B7D1',
    },
    {
      id: 4,
      title: 'Çekiliş Kampanyası',
      subtitle: 'Büyük ödüller kazanın',
      image: 'https://images.unsplash.com/photo-1513475382585-d06e58bcb0e0?w=800&h=400&fit=crop',
      backgroundColor: '#96CEB4',
    },
  ];

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentIndex((prevIndex) => {
        const nextIndex = (prevIndex + 1) % banners.length;
        scrollViewRef.current?.scrollTo({
          x: nextIndex * screenWidth,
          animated: true,
        });
        return nextIndex;
      });
    }, 5000); // 5 saniyede bir değişir

    return () => clearInterval(timer);
  }, [banners.length]);

  const handleScroll = (event) => {
    const scrollPosition = event.nativeEvent.contentOffset.x;
    const index = Math.round(scrollPosition / screenWidth);
    setCurrentIndex(index);
  };

  const handleDotPress = (index) => {
    setCurrentIndex(index);
    scrollViewRef.current?.scrollTo({
      x: index * screenWidth,
      animated: true,
    });
  };

  return (
    <Box className="relative">
      <ScrollView
        ref={scrollViewRef}
        horizontal
        pagingEnabled
        showsHorizontalScrollIndicator={false}
        onScroll={handleScroll}
        scrollEventThrottle={16}
        style={styles.scrollView}
      >
        {banners.map((banner, index) => (
          <TouchableOpacity
            key={banner.id}
            style={[styles.bannerContainer, { backgroundColor: banner.backgroundColor }]}
            activeOpacity={0.9}
          >
            <Box className="flex-1 flex-row items-center justify-between px-6">
              <Box className="flex-1">
                <Text className="text-white text-2xl font-bold mb-2">
                  {banner.title}
                </Text>
                <Text className="text-white/90 text-base">
                  {banner.subtitle}
                </Text>
              </Box>
              <Image
                source={{ uri: banner.image }}
                alt={banner.title}
                className="w-32 h-24 rounded-lg ml-4"
                style={styles.bannerImage}
              />
            </Box>
          </TouchableOpacity>
        ))}
      </ScrollView>

      {/* Dots Indicator */}
      <Box className="absolute bottom-4 left-0 right-0 flex-row justify-center">
        {banners.map((_, index) => (
          <TouchableOpacity
            key={index}
            style={[
              styles.dot,
              {
                backgroundColor: currentIndex === index ? COLORS.white : 'rgba(255,255,255,0.5)',
              },
            ]}
            onPress={() => handleDotPress(index)}
          />
        ))}
      </Box>
    </Box>
  );
};

const styles = {
  scrollView: {
    height: 180,
  },
  
  bannerContainer: {
    width: screenWidth,
    height: 180,
    justifyContent: 'center',
  },
  
  bannerImage: {
    resizeMode: 'cover',
  },
  
  dot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginHorizontal: 4,
  },
};
