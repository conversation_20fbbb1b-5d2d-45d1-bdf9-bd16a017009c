{"version": 3, "file": "filterStyles.js", "sourceRoot": "", "sources": ["../../src/css/filterStyles.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,UAAU,EAAE,MAAM,cAAc,CAAC;AAE1C,0DAA0D;AAC1D,uBAAuB;AACvB,MAAM,UAAU,GAAG;IACjB,gBAAgB;IAChB,gBAAgB;IAChB,oBAAoB;IACpB,mBAAmB;IACnB,mBAAmB;IACnB,eAAe;IACf,yBAAyB;IACzB,oBAAoB;IACpB,yBAAyB;IACzB,sBAAsB;IACtB,qBAAqB;IACrB,gBAAgB;IAChB,iBAAiB;IACjB,kBAAkB;IAClB,oBAAoB;IACpB,kBAAkB;IAClB,gBAAgB;IAChB,WAAW;IACX,WAAW;IACX,MAAM;IACN,QAAQ;IACR,QAAQ;IACR,iBAAiB;IACjB,cAAc;IACd,cAAc;IACd,eAAe;IACf,eAAe;IACf,iBAAiB;IACjB,YAAY;IACZ,YAAY;IACZ,cAAc;IACd,qBAAqB;IACrB,kBAAkB;IAClB,mBAAmB;IACnB,SAAS;IACT,cAAc;IACd,WAAW;IACX,WAAW;IACX,oBAAoB;IACpB,qBAAqB;IACrB,qBAAqB;IACrB,aAAa;IACb,mBAAmB;IACnB,aAAa;IACb,iBAAiB;IACjB,iBAAiB;IACjB,oBAAoB;IACpB,oBAAoB;IACpB,0BAA0B;IAC1B,YAAY;IACZ,YAAY;CACb,CAAC;AAEF,MAAM,UAAU,YAAY,CAAC,SAAS;IACpC,IAAI,CAAC,SAAS,EAAE;QACd,OAAO,SAAS,CAAC;KAClB;IACD,MAAM,KAAK,GAAG,UAAU,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;IAE5C,MAAM,aAAa,GAAG,MAAM,CAAC,WAAW,CACtC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAC/D,CAAC;IAEF,OAAO,mBAAmB,CAAC,aAAa,CAAC,CAAC;AAC5C,CAAC;AAED,SAAS,mBAAmB,CAAC,KAAK;IAChC,IAAI,CAAC,KAAK;QAAE,OAAO,KAAK,CAAC;IAEzB,IAAI,KAAK,CAAC,UAAU,EAAE;QACpB,IAAI,KAAK,CAAC,UAAU,KAAK,QAAQ,EAAE;YACjC,0BAA0B;YAC1B,KAAK,CAAC,OAAO,GAAG,CAAC,CAAC;SACnB;QACD,OAAO,KAAK,CAAC,UAAU,CAAC;KACzB;IAED,IAAI,KAAK,CAAC,QAAQ,EAAE;QAClB,IAAI,CAAC,CAAC,UAAU,EAAE,UAAU,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,QAAQ,CAAC,EAAE;YACtD,OAAO,CAAC,IAAI,CAAC,0BAA0B,KAAK,CAAC,QAAQ,GAAG,CAAC,CAAC;YAC1D,KAAK,CAAC,QAAQ,GAAG,UAAU,CAAC;SAC7B;KACF;IAED,OAAO,KAAK,CAAC;AACf,CAAC", "sourcesContent": ["import { StyleSheet } from 'react-native';\n\n// Remove the unsupported web styles from the style object\n// to prevent crashing.\nconst WEB_STYLES = [\n  'backdropFilter',\n  'animationDelay',\n  'animationDirection',\n  'animationDuration',\n  'animationFillMode',\n  'animationName',\n  'animationIterationCount',\n  'animationPlayState',\n  'animationTimingFunction',\n  'backgroundAttachment',\n  'backgroundBlendMode',\n  'backgroundClip',\n  'backgroundImage',\n  'backgroundOrigin',\n  'backgroundPosition',\n  'backgroundRepeat',\n  'backgroundSize',\n  'boxShadow',\n  'boxSizing',\n  'clip',\n  'cursor',\n  'filter',\n  'gridAutoColumns',\n  'gridAutoFlow',\n  'gridAutoRows',\n  'gridColumnEnd',\n  'gridColumnGap',\n  'gridColumnStart',\n  'gridRowEnd',\n  'gridRowGap',\n  'gridRowStart',\n  'gridTemplateColumns',\n  'gridTemplateRows',\n  'gridTemplateAreas',\n  'outline',\n  'outlineColor',\n  'overflowX',\n  'overflowY',\n  'overscrollBehavior',\n  'overscrollBehaviorX',\n  'overscrollBehaviorY',\n  'perspective',\n  'perspectiveOrigin',\n  'touchAction',\n  'transformOrigin',\n  'transitionDelay',\n  'transitionDuration',\n  'transitionProperty',\n  'transitionTimingFunction',\n  'userSelect',\n  'willChange',\n];\n\nexport function filterStyles(styleProp) {\n  if (!styleProp) {\n    return styleProp;\n  }\n  const style = StyleSheet.flatten(styleProp);\n\n  const filteredStyle = Object.fromEntries(\n    Object.entries(style).filter(([k]) => !WEB_STYLES.includes(k))\n  );\n\n  return processNativeStyles(filteredStyle);\n}\n\nfunction processNativeStyles(style) {\n  if (!style) return style;\n\n  if (style.visibility) {\n    if (style.visibility === 'hidden') {\n      // style.display = \"none\";\n      style.opacity = 0;\n    }\n    delete style.visibility;\n  }\n\n  if (style.position) {\n    if (!['absolute', 'relative'].includes(style.position)) {\n      console.warn(`Unsupported position: '${style.position}'`);\n      style.position = 'relative';\n    }\n  }\n\n  return style;\n}\n"]}