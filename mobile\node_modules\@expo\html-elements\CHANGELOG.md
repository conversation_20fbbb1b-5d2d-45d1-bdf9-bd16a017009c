# Changelog

## Unpublished

### 🛠 Breaking changes

### 🎉 New features

### 🐛 Bug fixes

- Prevent babel plugin from running on node_modules. ([#21594](https://github.com/expo/expo/pull/21594) by [@EvanBacon](https://github.com/EvanBacon))
- Prevent babel plugin from transforming `html` and `body` on web. ([#21594](https://github.com/expo/expo/pull/21594) by [@EvanBacon](https://github.com/EvanBacon))

### 💡 Others

## 0.4.1 — 2023-02-09

### 🎉 New features

- Strip unsupported web styles on native platforms. ([#21069](https://github.com/expo/expo/pull/21069) by [@EvanBacon](https://github.com/EvanBacon))
- Provide better assertions for text children in View components in development-mode. ([#21069](https://github.com/expo/expo/pull/21069) by [@EvanBacon](https://github.com/EvanBacon))
