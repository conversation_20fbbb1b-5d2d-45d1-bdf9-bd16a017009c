name: Test CI

on:
  pull_request:
  push:
    branches:
      - master

jobs:
  build:
    runs-on: ubuntu-latest

    strategy:
      matrix:
        node-version: [16.x, 18.x, 20.x, 21.x]

    steps:
      - uses: actions/checkout@v1
      - name: Use Node.js ${{ matrix.node-version }}
        uses: actions/setup-node@v1
        with:
          node-version: ${{ matrix.node-version }}
      - name: npm ci, lint and test
        run: |
            npm ci
            npm run lint
            npm run test:coverage
      - name: coverage
        uses: codecov/codecov-action@v1.0.5
        if: matrix.node-version == '13.x'
        with:
          token: ${{ secrets.CODECOV_TOKEN }}
