import { Stack } from 'expo-router/stack';
import { Redirect } from 'expo-router';

// Mock user for web
const useMockUser = () => ({
  isSignedIn: true,
  isLoaded: true,
});

export default function Layout() {
    const { isSignedIn ,isLoaded} = useMockUser();

    if (!isLoaded) {
      console.log('Loading...');
       return null;
      }

    if (!isSignedIn) {
    return <Redirect href={'/(auth)/sign-in'} />
  }
  return <Stack screenOptions={{ headerShown: false }}/>
}