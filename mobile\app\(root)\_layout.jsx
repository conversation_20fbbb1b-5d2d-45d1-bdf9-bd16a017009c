import { Stack } from 'expo-router/stack';
import { Redirect } from 'expo-router';
import { Platform } from 'react-native';

// Conditional import for useUser
let useUser;
if (Platform.OS === 'web') {
  // Mock user for web
  useUser = () => ({ isSignedIn: true, isLoaded: true });
} else {
  try {
    const clerk = require('@clerk/clerk-expo');
    useUser = clerk.useUser;
  } catch (error) {
    // Fallback to mock
    useUser = () => ({ isSignedIn: true, isLoaded: true });
  }
}

export default function Layout() {
    const { isSignedIn ,isLoaded} = useUser();

    if (!isLoaded) {
      console.log('Loading...');
       return null;
      }

    if (!isSignedIn) {
    return <Redirect href={'/(auth)/sign-in'} />
  }
  return <Stack screenOptions={{ headerShown: false }}/>
}