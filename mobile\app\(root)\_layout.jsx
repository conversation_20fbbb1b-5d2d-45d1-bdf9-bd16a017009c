import { Stack } from 'expo-router/stack';
import { Redirect } from 'expo-router';
import  {useUser} from '@clerk/clerk-expo';

export default function Layout() {
    const { isSignedIn ,isLoaded} = useUser();


    if (!isLoaded) {
      console.log('Loading...');
       return null;
      }

    if (!isSignedIn) {
    return <Redirect href={'/(auth)/sign-in'} />
  }
  return <Stack screenOptions={{ headerShown: false }}/>
}