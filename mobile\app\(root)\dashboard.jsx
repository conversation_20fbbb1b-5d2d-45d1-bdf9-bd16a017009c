import React, { useState } from 'react';
import { View, Text, TouchableOpacity, ScrollView } from 'react-native';
import { useUser } from '@clerk/clerk-expo';
import { useRouter } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import { COLORS } from '@/constants/colors';
import { Navbar } from '../../components/Navbar';
import { styles } from '../../assets/styles/dashboard.styles';
import { sidebarItems } from './sidebarItems';

export default function Dashboard() {
  const { user } = useUser();
  const router = useRouter();
  const [searchQuery, setSearchQuery] = useState('');
  const [activeSection, setActiveSection] = useState('products');


  const renderSidebarItems = () => {
    return sidebarItems.map((item) => (
      <TouchableOpacity
        key={item.id}
        style={[
          styles.sidebarItem,
          activeSection === item.id && styles.sidebarItemActive
        ]}
        onPress={() => handleSectionChange(item.id)}
      >
        <Ionicons 
          name={item.icon} 
          size={24} 
          color={activeSection === item.id ? COLORS.white : COLORS.text} 
        />
        <View style={styles.sidebarItemContent}>
          <Text style={[
            styles.sidebarItemTitle,
            activeSection === item.id && styles.sidebarItemTitleActive
          ]}>
            {item.title}
          </Text>
          <Text style={[
            styles.sidebarItemDescription,
            activeSection === item.id && styles.sidebarItemDescriptionActive
          ]}>
            {item.description}
          </Text>
        </View>
      </TouchableOpacity>
    ));
  };



  const handleSectionChange = (sectionId) => {
    setActiveSection(sectionId);
    // Dashboard ana sayfasında kalıyoruz, sadece aktif bölümü değiştiriyoruz
  };

  const renderContent = () => {
    switch (activeSection) {
      case 'products':
        return (
          <View style={styles.contentArea}>
            <Text style={styles.contentTitle}>Ürün Yönetimi</Text>
            <Text style={styles.contentDescription}>
              Burada ürünlerinizi yönetebilirsiniz. Yeni ürün ekleyebilir, mevcut ürünleri düzenleyebilir veya silebilirsiniz.
            </Text>
            
            <View style={styles.actionButtons}>
              <TouchableOpacity 
                style={styles.actionButton}
                onPress={() => router.push('/dashboard/products/add')}
              >
                <Ionicons name="add-circle" size={24} color={COLORS.white} />
                <Text style={styles.actionButtonText}>Yeni Ürün Ekle</Text>
              </TouchableOpacity>
              
              <TouchableOpacity 
                style={[styles.actionButton, styles.secondaryButton]}
                onPress={() => router.push('/dashboard/products/list')}
              >
                <Ionicons name="list" size={24} color={COLORS.primary} />
                <Text style={[styles.actionButtonText, styles.secondaryButtonText]}>Ürünleri Listele</Text>
              </TouchableOpacity>
            </View>
          </View>
        ); 
      case 'categories':
        return (
          <View style={styles.contentArea}>
            <Text style={styles.contentTitle}>Kategori Yönetimi</Text>
            <Text style={styles.contentDescription}>
              Burada kategorilerinizi yönetebilirsiniz. Yeni kategori ekleyebilir, mevcut kategorileri düzenleyebilir veya silebilirsiniz.
            </Text>
            
            <View style={styles.actionButtons}>
              <TouchableOpacity 
                style={styles.actionButton}
                onPress={() => router.push('/dashboard/categories/add')}
              >
                <Ionicons name="add-circle" size={24} color={COLORS.white} />
                <Text style={styles.actionButtonText}>Yeni Kategori Ekle</Text>
              </TouchableOpacity>
              
              <TouchableOpacity 
                style={[styles.actionButton, styles.secondaryButton]}
                onPress={() => router.push('/dashboard/categories/list')}
              >
                <Ionicons name="list" size={24} color={COLORS.primary} />
                <Text style={[styles.actionButtonText, styles.secondaryButtonText]}>Kategorileri Listele</Text>
              </TouchableOpacity>
            </View>
          </View>
        );
        case 'subcategories':
        return (
          <View style={styles.contentArea}>
            <Text style={styles.contentTitle}>Alt Kategori Yönetimi</Text>
            <Text style={styles.contentDescription}>
              Burada alt kategorilerinizi yönetebilirsiniz. Yeni alt kategori ekleyebilir, mevcut alt kategorileri düzenleyebilir veya silebilirsiniz.
            </Text>
            
            <View style={styles.actionButtons}>
              <TouchableOpacity 
                style={styles.actionButton}
                onPress={() => router.push('/dashboard/subcategories/add')}
              >
                <Ionicons name="add-circle" size={24} color={COLORS.white} />
                <Text style={styles.actionButtonText}>Yeni Alt Kategori Ekle</Text>
              </TouchableOpacity>
              
              <TouchableOpacity 
                style={[styles.actionButton, styles.secondaryButton]}
                onPress={() => router.push('/dashboard/subcategories/list')}
              >
                <Ionicons name="list" size={24} color={COLORS.primary} />
                <Text style={[styles.actionButtonText, styles.secondaryButtonText]}>Alt Kategorileri Listele</Text>
              </TouchableOpacity>
            </View>
          </View>
        );
      case 'users':
        return (
          <View style={styles.contentArea}>
            <Text style={styles.contentTitle}>Kullanıcı Yönetimi</Text>
            <Text style={styles.contentDescription}>
              Burada kullanıcıları yönetebilirsiniz. Yeni kullanıcı ekleyebilir, mevcut kullanıcıları düzenleyebilir veya silebilirsiniz.
            </Text>
            
            <View style={styles.actionButtons}>
              <TouchableOpacity 
                style={styles.actionButton}
                onPress={() => router.push('/dashboard/users/add')}
              >
                <Ionicons name="add-circle" size={24} color={COLORS.white} />
                <Text style={styles.actionButtonText}>Yeni Kullanıcı Ekle</Text>
              </TouchableOpacity>
              
              <TouchableOpacity 
                style={[styles.actionButton, styles.secondaryButton]}
                onPress={() => router.push('/dashboard/users/list')}
              >
                <Ionicons name="list" size={24} color={COLORS.primary} />
                <Text style={[styles.actionButtonText, styles.secondaryButtonText]}>Kullanıcıları Listele</Text>
              </TouchableOpacity>
            </View>
          </View>
        );
      case 'auctions':
        return (
          <View style={styles.contentArea}>
            <Text style={styles.contentTitle}>Açık Artırma Yönetimi</Text>
            <Text style={styles.contentDescription}>
              Burada açık artırma yönetebilirsiniz. Yeni açık artırma ekleyebilir, mevcut açık artırma düzenleyebilir veya silebilirsiniz.
            </Text>
            
            <View style={styles.actionButtons}>
              <TouchableOpacity 
                style={styles.actionButton}
                onPress={() => router.push('/dashboard/auctions/add')}
              >
                <Ionicons name="add-circle" size={24} color={COLORS.white} />
                <Text style={styles.actionButtonText}>Yeni Açık Artırma Ekle</Text>
              </TouchableOpacity>
              
              <TouchableOpacity 
                style={[styles.actionButton, styles.secondaryButton]}
                onPress={() => router.push('/dashboard/auctions/list')}
              >
                <Ionicons name="list" size={24} color={COLORS.primary} />
                <Text style={[styles.actionButtonText, styles.secondaryButtonText]}>Açık Artırmaları Listele</Text>
              </TouchableOpacity>
            </View>
          </View>
        );  
        case 'lottery':
        return (
          <View style={styles.contentArea}>
            <Text style={styles.contentTitle}>Çekiliş Yönetimi</Text>
            <Text style={styles.contentDescription}>
              Burada çekiliş yönetebilirsiniz. Yeni çekiliş ekleyebilir, mevcut çekiliş düzenleyebilir veya silebilirsiniz.
            </Text>
            
            <View style={styles.actionButtons}>
              <TouchableOpacity 
                style={styles.actionButton}
                onPress={() => router.push('/dashboard/lottery/add')}
              >
                <Ionicons name="add-circle" size={24} color={COLORS.white} />
                <Text style={styles.actionButtonText}>Yeni Çekiliş Ekle</Text>
              </TouchableOpacity>
              
              <TouchableOpacity 
                style={[styles.actionButton, styles.secondaryButton]}
                onPress={() => router.push('/dashboard/lottery/list')}
              >
                <Ionicons name="list" size={24} color={COLORS.primary} />
                <Text style={[styles.actionButtonText, styles.secondaryButtonText]}>Çekilişleri Listele</Text>
              </TouchableOpacity>
            </View>
          </View>
        );
        case 'exchange':
        return (
          <View style={styles.contentArea}>
            <Text style={styles.contentTitle}>Takas Yönetimi</Text>
            <Text style={styles.contentDescription}>
              Burada takas yönetebilirsiniz. Yeni takas ekleyebilir, mevcut takas düzenleyebilir veya silebilirsiniz.
            </Text>
            
            <View style={styles.actionButtons}>
              <TouchableOpacity 
                style={styles.actionButton}
                onPress={() => router.push('/dashboard/exchange/add')}
              >
                <Ionicons name="add-circle" size={24} color={COLORS.white} />
                <Text style={styles.actionButtonText}>Yeni Takas Ekle</Text>
              </TouchableOpacity>
              
              <TouchableOpacity 
                style={[styles.actionButton, styles.secondaryButton]}
                onPress={() => router.push('/dashboard/exchange/list')}
              >
                <Ionicons name="list" size={24} color={COLORS.primary} />
                <Text style={[styles.actionButtonText, styles.secondaryButtonText]}>Takasları Listele</Text>
              </TouchableOpacity>
            </View>
          
          </View>
        );
        case 'dynamicPricing':
        return (
          <View style={styles.contentArea}>
            <Text style={styles.contentTitle}>Dinamik Fiyatlama Yönetimi</Text>
            <Text style={styles.contentDescription}>
              Burada dinamik fiyatlamayı yönetebilirsiniz. Yeni dinamik fiyatlama ekleyebilir, mevcut dinamik fiyatlamayı düzenleyebilir veya silebilirsiniz.
            </Text>

            <View style={styles.actionButtons}>
              <TouchableOpacity 
                style={styles.actionButton}
                onPress={() => router.push('/dashboard/dynamicPricing/add')}
              >
                <Ionicons name="add-circle" size={24} color={COLORS.white} />
                <Text style={styles.actionButtonText}>Yeni Dinamik Fiyatlama Ekle</Text>
              </TouchableOpacity>
              
              <TouchableOpacity 
                style={[styles.actionButton, styles.secondaryButton]}
                onPress={() => router.push('/dashboard/dynamicPricing/list')}
              >
                <Ionicons name="list" size={24} color={COLORS.primary} />
                <Text style={[styles.actionButtonText, styles.secondaryButtonText]}>Dinamik Fiyatlamaları Listele</Text>
              </TouchableOpacity>
            </View>
          </View>
        );
        case 'banner':
        return (
          <View style={styles.contentArea}>
            <Text style={styles.contentTitle}>Banner Yönetimi</Text>
            <Text style={styles.contentDescription}>
              Burada banner yönetebilirsiniz. Yeni banner ekleyebilir, mevcut banner düzenleyebilir veya silebilirsiniz.
            </Text>
            
            <View style={styles.actionButtons}>
              <TouchableOpacity 
                style={styles.actionButton}
                onPress={() => router.push('/dashboard/banner/add')}
              >
                <Ionicons name="add-circle" size={24} color={COLORS.white} />
                <Text style={styles.actionButtonText}>Yeni Banner Ekle</Text>
              </TouchableOpacity>
              
              <TouchableOpacity 
                style={[styles.actionButton, styles.secondaryButton]}
                onPress={() => router.push('/dashboard/banner/list')}
              >
                <Ionicons name="list" size={24} color={COLORS.primary} />
                <Text style={[styles.actionButtonText, styles.secondaryButtonText]}>Bannerları Listele</Text>
              </TouchableOpacity>
              </View>
          </View>
        );

        case 'settings':
        return (
          <View style={styles.contentArea}>
            <Text style={styles.contentTitle}>Site Ayarları</Text>
            <Text style={styles.contentDescription}>
              Burada site ayarlarını yönetebilirsiniz. Site genel ayarlarını düzenleyebilirsiniz.
            </Text>
            
            <View style={styles.actionButtons}>
              <TouchableOpacity 
                style={styles.actionButton}
                onPress={() => router.push('/dashboard/settings/general')}
              >
                <Ionicons name="settings" size={24} color={COLORS.white} />
                <Text style={styles.actionButtonText}>Genel Ayarlar</Text>
              </TouchableOpacity>
            </View>
          </View>
        );
      default:
        return null;
    }
  };


  return (
    <View style={styles.container}>
      <Navbar 
        user={user} 
        searchQuery={searchQuery} 
        setSearchQuery={setSearchQuery}
        router={router}
      />
      
      <View style={styles.dashboardContent}>
        {/* Sol Sidebar */}
        <View style={styles.sidebar}>
          <Text style={styles.sidebarTitle}>Dashboard</Text>
          
        <ScrollView style={styles.mainContent}>
          {renderSidebarItems()}
        </ScrollView>

        </View>
        
        {/* Ana İçerik */}
        <ScrollView style={styles.mainContent}>
          {renderContent()}
        </ScrollView>

      </View>
    </View>
  );
}
