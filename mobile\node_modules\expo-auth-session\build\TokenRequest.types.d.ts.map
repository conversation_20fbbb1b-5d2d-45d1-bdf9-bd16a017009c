{"version": 3, "file": "TokenRequest.types.d.ts", "sourceRoot": "", "sources": ["../src/TokenRequest.types.ts"], "names": [], "mappings": "AACA;;;;GAIG;AACH,MAAM,MAAM,SAAS,GAAG,QAAQ,GAAG,KAAK,CAAC;AAGzC;;;;GAIG;AACH,oBAAY,aAAa;IACvB;;;;OAIG;IACH,WAAW,iBAAiB;IAC5B;;;;OAIG;IACH,YAAY,kBAAkB;CAC/B;AAGD;;GAEG;AACH,MAAM,MAAM,kBAAkB,GAAG;IAC/B;;;;;;;;OAQG;IACH,QAAQ,EAAE,MAAM,CAAC;IACjB;;;;;OAKG;IACH,YAAY,CAAC,EAAE,MAAM,CAAC;IACtB;;OAEG;IACH,WAAW,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;IACrC;;;;OAIG;IACH,MAAM,CAAC,EAAE,MAAM,EAAE,CAAC;CACnB,CAAC;AAGF;;;;GAIG;AACH,MAAM,MAAM,wBAAwB,GAAG,kBAAkB,GAAG;IAC1D;;OAEG;IACH,IAAI,EAAE,MAAM,CAAC;IACb;;;;OAIG;IACH,WAAW,EAAE,MAAM,CAAC;CACrB,CAAC;AAGF;;;;GAIG;AACH,MAAM,MAAM,yBAAyB,GAAG,kBAAkB,GAAG;IAC3D;;OAEG;IACH,YAAY,CAAC,EAAE,MAAM,CAAC;CACvB,CAAC;AAGF;;;;GAIG;AACH,MAAM,MAAM,wBAAwB,GAAG,OAAO,CAAC,kBAAkB,CAAC,GAAG;IACnE;;;;OAIG;IACH,KAAK,EAAE,MAAM,CAAC;IACd;;;;OAIG;IACH,aAAa,CAAC,EAAE,aAAa,CAAC;CAC/B,CAAC;AAGF;;;;GAIG;AACH,oBAAY,SAAS;IACnB;;;;OAIG;IACH,iBAAiB,uBAAuB;IACxC;;;;OAIG;IACH,QAAQ,aAAa;IACrB;;;;OAIG;IACH,YAAY,kBAAkB;IAC9B;;;;OAIG;IACH,iBAAiB,uBAAuB;CACzC;AAGD;;GAEG;AACH,MAAM,MAAM,yBAAyB,GAAG;IACtC,YAAY,EAAE,MAAM,CAAC;IACrB,UAAU,CAAC,EAAE,SAAS,CAAC;IACvB,UAAU,CAAC,EAAE,MAAM,CAAC;IACpB,aAAa,CAAC,EAAE,MAAM,CAAC;IACvB,KAAK,CAAC,EAAE,MAAM,CAAC;IACf,QAAQ,CAAC,EAAE,MAAM,CAAC;IAClB,SAAS,CAAC,EAAE,MAAM,CAAC;CACpB,CAAC;AAGF,MAAM,MAAM,mBAAmB,GAAG;IAChC;;;;OAIG;IACH,WAAW,EAAE,MAAM,CAAC;IACpB;;;;OAIG;IACH,SAAS,CAAC,EAAE,SAAS,CAAC;IACtB;;;;;;;;;;OAUG;IACH,SAAS,CAAC,EAAE,MAAM,CAAC;IACnB;;;;OAIG;IACH,YAAY,CAAC,EAAE,MAAM,CAAC;IACtB;;;;OAIG;IACH,KAAK,CAAC,EAAE,MAAM,CAAC;IACf;;;;;OAKG;IACH,KAAK,CAAC,EAAE,MAAM,CAAC;IACf;;;;OAIG;IACH,OAAO,CAAC,EAAE,MAAM,CAAC;IACjB;;OAEG;IACH,QAAQ,CAAC,EAAE,MAAM,CAAC;CACnB,CAAC"}