import { StyleSheet } from 'react-native';
import { COLORS } from '../../constants/colors';

export const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  
  formContainer: {
    flex: 1,
    padding: 20,
  },
  
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 30,
    paddingBottom: 15,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.border,
  },
  
  backButton: {
    padding: 10,
    borderRadius: 8,
    backgroundColor: COLORS.card,
    marginRight: 15,
  },
  
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: COLORS.text,
    flex: 1,
  },
  
  form: {
    backgroundColor: COLORS.card,
    borderRadius: 15,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 3,
  },
  
  inputGroup: {
    marginBottom: 20,
  },
  
  label: {
    fontSize: 16,
    fontWeight: '600',
    color: COLORS.text,
    marginBottom: 8,
  },
  
  input: {
    borderWidth: 1,
    borderColor: COLORS.border,
    borderRadius: 10,
    paddingHorizontal: 15,
    paddingVertical: 12,
    fontSize: 16,
    color: COLORS.text,
    backgroundColor: COLORS.background,
    // Web için
    outlineStyle: 'none',
  },
  
  textArea: {
    minHeight: 100,
    textAlignVertical: 'top',
  },
  
  row: {
    flexDirection: 'row',
    gap: 15,
  },
  
  halfWidth: {
    flex: 1,
  },
  
  pickerContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 10,
  },
  
  categoryOption: {
    paddingHorizontal: 15,
    paddingVertical: 10,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: COLORS.border,
    backgroundColor: COLORS.background,
  },
  
  categoryOptionSelected: {
    backgroundColor: COLORS.primary,
    borderColor: COLORS.primary,
  },
  
  categoryOptionText: {
    fontSize: 14,
    color: COLORS.text,
    fontWeight: '500',
  },
  
  categoryOptionTextSelected: {
    color: COLORS.white,
  },
  
  checkboxGroup: {
    marginBottom: 30,
    gap: 15,
  },

  checkbox: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 10,
    paddingVertical: 5,
  },
  
  checkboxLabel: {
    fontSize: 16,
    color: COLORS.text,
    fontWeight: '500',
  },
  
  submitButton: {
    backgroundColor: COLORS.primary,
    borderRadius: 10,
    paddingVertical: 15,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 3,
  },
  
  submitButtonDisabled: {
    backgroundColor: COLORS.textLight,
    opacity: 0.6,
  },
  
  submitButtonText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: COLORS.white,
  },
  
  // Responsive design
  '@media (max-width: 768px)': {
    row: {
      flexDirection: 'column',
      gap: 0,
    },
    
    halfWidth: {
      flex: 'none',
    },
    
    pickerContainer: {
      flexDirection: 'column',
    },
    
    categoryOption: {
      alignSelf: 'stretch',
      textAlign: 'center',
    },
  },
});
