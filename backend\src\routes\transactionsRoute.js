
import express from "express"
import {getTransactionsByUserId} from "../controllers/transactionsController.js";
import {createTransaction} from "../controllers/transactionsController.js";
import {deleteTransaction} from "../controllers/transactionsController.js";
import {putTransaction} from "../controllers/transactionsController.js";
import {sumaryTransactionsByUserId} from "../controllers/transactionsController.js";

const router = express.Router()

router.get('/:userId', getTransactionsByUserId);
router.post('/',createTransaction); 
router.delete('/:id',deleteTransaction); 
router.put('/:id',putTransaction);
router.get('/summary/:userId', sumaryTransactionsByUserId);



export default router;