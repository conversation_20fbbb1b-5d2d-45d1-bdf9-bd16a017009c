import React, { useState, useEffect } from 'react';
import { View, Text, TextInput, TouchableOpacity, ScrollView, Alert } from 'react-native';
import { useUser } from '@clerk/clerk-expo';
import { useRouter, useLocalSearchParams } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import { COLORS } from '@/constants/colors';
import { API_URL } from '@/constants/api';
import { Navbar } from '../../../../../components/Navbar';
import { styles } from '../../../../../assets/styles/form.styles';

export default function EditCategory() {
  const { user } = useUser();
  const router = useRouter();
  const { id } = useLocalSearchParams();
  const [searchQuery, setSearchQuery] = useState('');
  const [loading, setLoading] = useState(false);
  const [categoryLoading, setCategoryLoading] = useState(true);
  const [categoryName, setCategoryName] = useState('');

  useEffect(() => {
    if (id) {
      fetchCategory();
    }
  }, [id]);

  const fetchCategory = async () => {
    try {
      setCategoryLoading(true);
      const response = await fetch(`${API_URL}/categories/${id}`);
      
      if (response.ok) {
        const category = await response.json();
        setCategoryName(category.name || '');
      } else {
        Alert.alert('Hata', 'Kategori bilgileri yüklenemedi');
        router.back();
      }
    } catch (error) {
      console.error('Error fetching category:', error);
      Alert.alert('Hata', 'Kategori bilgileri yüklenirken bir hata oluştu');
      router.back();
    } finally {
      setCategoryLoading(false);
    }
  };

  const handleSubmit = async () => {
    try {
      // Validation
      if (!categoryName.trim()) {
        Alert.alert('Hata', 'Lütfen kategori adını girin');
        return;
      }

      setLoading(true);

      const response = await fetch(`${API_URL}/categories/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ name: categoryName.trim() }),
      });

      if (response.ok) {
        Alert.alert('Başarılı', 'Kategori başarıyla güncellendi', [
          { text: 'Tamam', onPress: () => router.back() }
        ]);
      } else {
        const errorData = await response.json();
        Alert.alert('Hata', errorData.message || 'Kategori güncellenirken bir hata oluştu');
      }
    } catch (error) {
      console.error('Error updating category:', error);
      Alert.alert('Hata', 'Kategori güncellenirken bir hata oluştu');
    } finally {
      setLoading(false);
    }
  };

  if (categoryLoading) {
    return (
      <View style={styles.container}>
        <Navbar 
          user={user} 
          searchQuery={searchQuery} 
          setSearchQuery={setSearchQuery}
          router={router}
        />
        <View style={styles.loadingContainer}>
          <Text style={styles.loadingText}>Kategori bilgileri yükleniyor...</Text>
        </View>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <Navbar 
        user={user} 
        searchQuery={searchQuery} 
        setSearchQuery={setSearchQuery}
        router={router}
      />
      
      <ScrollView style={styles.formContainer}>
        <View style={styles.header}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => router.back()}
          >
            <Ionicons name="arrow-back" size={24} color={COLORS.text} />
          </TouchableOpacity>
          <Text style={styles.title}>Kategori Düzenle</Text>
        </View>

        <View style={styles.form}>
          <View style={styles.inputGroup}>
            <Text style={styles.label}>Kategori Adı *</Text>
            <TextInput
              style={styles.input}
              value={categoryName}
              onChangeText={setCategoryName}
              placeholder="Kategori adını girin"
              placeholderTextColor={COLORS.textLight}
            />
          </View>

          <TouchableOpacity
            style={[styles.submitButton, loading && styles.submitButtonDisabled]}
            onPress={handleSubmit}
            disabled={loading}
          >
            <Text style={styles.submitButtonText}>
              {loading ? 'Güncelleniyor...' : 'Kategori Güncelle'}
            </Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </View>
  );
}
