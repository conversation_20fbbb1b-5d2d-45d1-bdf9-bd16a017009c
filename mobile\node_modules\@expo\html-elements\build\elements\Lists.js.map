{"version": 3, "file": "Lists.js", "sourceRoot": "", "sources": ["../../src/elements/Lists.tsx"], "names": [], "mappings": "AAAA,OAAO,KAAK,EAAE,EAAiB,UAAU,EAAqB,MAAM,OAAO,CAAC;AAC5E,OAAO,EAAE,QAAQ,EAAE,MAAM,cAAc,CAAC;AAExC,OAAO,IAAmB,MAAM,oBAAoB,CAAC;AACrD,OAAO,IAAmB,MAAM,oBAAoB,CAAC;AAErD,SAAS,UAAU,CAAC,cAAyB,EAAE;IAC7C,OAAO,UAAU,CAAC,CAAC,KAAgB,EAAE,GAAG,EAAE,EAAE;QAC1C,OAAO,oBAAC,IAAI,OAAK,WAAW,KAAM,KAAK,EAAE,GAAG,EAAE,GAAG,GAAI,CAAC;IACxD,CAAC,CAA6B,CAAC;AACjC,CAAC;AAED,MAAM,CAAC,MAAM,EAAE,GAAG,UAAU,CAC1B,QAAQ,CAAC,MAAM,CAAC;IACd,GAAG,EAAE;QACH,iBAAiB,EAAE,MAAM;KAC1B;CACF,CAAC,CACH,CAAC;AAEF,SAAS,WAAW,CAAC,KAAU;IAC7B,qCAAqC;IACrC,OAAO,OAAO,KAAK,CAAC,QAAQ,KAAK,QAAQ,CAAC;AAC5C,CAAC;AAID,MAAM,CAAC,MAAM,EAAE,GAAG,UAAU,CAAC,CAAC,KAAiC,EAAE,GAAQ,EAAE,EAAE;IAC3E,IAAI,WAAW,CAAC,KAAK,CAAC,EAAE;QACtB,MAAM,iBAAiB,GAAiC,QAAQ,CAAC,MAAM,CAAC;YACtE,GAAG,EAAE,UAAU;YACf,OAAO,EAAE,KAAK,CAAC,iBAAiB;SACjC,CAAC,CAAC;QACH,OAAO,oBAAC,IAAI,OAAK,KAAK,EAAE,iBAAiB,EAAE,iBAAiB,EAAE,GAAG,EAAE,GAAG,GAAI,CAAC;KAC5E;IACD,MAAM,iBAAiB,GAAiC,QAAQ,CAAC,MAAM,CAAC;QACtE,GAAG,EAAE,UAAU;QACf,OAAO,EAAE,KAAK,CAAC,iBAAiB;KACjC,CAAC,CAAC;IACH,OAAO,oBAAC,IAAI,OAAK,KAAK,EAAE,iBAAiB,EAAE,iBAAiB,EAAE,GAAG,EAAE,GAAG,GAAI,CAAC;AAC7E,CAAC,CAA2B,CAAC", "sourcesContent": ["import React, { ComponentType, forwardRef, PropsWithChildren } from 'react';\nimport { Platform } from 'react-native';\n\nimport Text, { TextProps } from '../primitives/Text';\nimport View, { ViewProps } from '../primitives/View';\n\nfunction createView(nativeProps: ViewProps = {}): ComponentType<ViewProps> {\n  return forwardRef((props: ViewProps, ref) => {\n    return <View {...nativeProps} {...props} ref={ref} />;\n  }) as ComponentType<ViewProps>;\n}\n\nexport const UL = createView(\n  Platform.select({\n    web: {\n      accessibilityRole: 'list',\n    },\n  })\n);\n\nfunction isTextProps(props: any): props is TextProps {\n  // Treat <li></li> as a Text element.\n  return typeof props.children === 'string';\n}\n\ntype LIProps = TextProps | ViewProps;\n\nexport const LI = forwardRef((props: PropsWithChildren<LIProps>, ref: any) => {\n  if (isTextProps(props)) {\n    const accessibilityRole: LIProps['accessibilityRole'] = Platform.select({\n      web: 'listitem',\n      default: props.accessibilityRole,\n    });\n    return <Text {...props} accessibilityRole={accessibilityRole} ref={ref} />;\n  }\n  const accessibilityRole: LIProps['accessibilityRole'] = Platform.select({\n    web: 'listitem',\n    default: props.accessibilityRole,\n  });\n  return <View {...props} accessibilityRole={accessibilityRole} ref={ref} />;\n}) as ComponentType<LIProps>;\n"]}