import { useUser } from '@clerk/clerk-expo'
import { Text, View, ScrollView, TouchableOpacity } from 'react-native'
import { useRouter } from 'expo-router'
import { COLORS } from '@/constants/colors'
import { Ionicons } from '@expo/vector-icons'
import { useState } from 'react'
import { Navbar } from '../../components/Navbar'
import { styles } from '../../assets/styles/home.styles'

export default function Page() {
  const { user } = useUser();
  const router = useRouter();
  const [searchQuery, setSearchQuery] = useState('');

  return (
    <View style={styles.container}>
      <Navbar
        user={user}
        searchQuery={searchQuery}
        setSearchQuery={setSearchQuery}
        router={router}
      />

      <ScrollView style={styles.mainContent}>
        <View style={styles.heroSection}>
          <Text style={styles.heroTitle}>{user?.firstName} Hoş Geldiniz</Text>
          <Text style={styles.heroSubtitle}>
            <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, çekilişler ve takas fırsatlar<PERSON>n<PERSON> keşfedin
          </Text>
        </View>
        <View>
          <Text style={styles.sectionTitle}><PERSON>an banner olacak her 5 saniyede bir</Text>
              <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.categoriesScroll}>
            {['açık artırmalar', 'çekilişler', 'takaslar', ].map((category, index) => (
              <TouchableOpacity key={index} style={styles.categoryCard}>
                <Ionicons name="cube-outline" size={24} color={COLORS.primary} />
                <Text style={styles.categoryText}>{category}</Text>
              </TouchableOpacity>
              ))}
          </ScrollView>
        </View>

        <View style={styles.categoriesSection}>
          <Text style={styles.sectionTitle}>Kategoriler</Text>
          <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.categoriesScroll}>
            {['Elektronik', 'Giyim', 'Ev & Yaşam', 'Spor', 'Kitap', 'Oyuncak'].map((category, index) => (
              <TouchableOpacity key={index} style={styles.categoryCard}>
                <Ionicons name="cube-outline" size={24} color={COLORS.secondary} />
                <Text style={styles.categoryText}>{category}</Text>
              </TouchableOpacity>
            ))}
          </ScrollView>
        </View>

        <View style={styles.featuredSection}>
          <Text style={styles.sectionTitle}>Öne Çıkan Ürünler</Text>
          <View style={styles.productGrid}>
            {[1, 2, 3, 4].map((item) => (
              <TouchableOpacity key={item} style={styles.productCard}>
                <View style={styles.productImage}>
                  <Ionicons name="image-outline" size={40} color={COLORS.textLight} />
                </View>
                <Text style={styles.productName}>Ürün {item}</Text>
                <Text style={styles.productPrice}>₺{(item * 100).toFixed(2)}</Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>
      </ScrollView>
    </View>
  );
}
