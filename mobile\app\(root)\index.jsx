import { useUser } from '@clerk/clerk-expo'
import { Text, View, ScrollView, TouchableOpacity } from 'react-native'
import { useRouter } from 'expo-router'
import { COLORS } from '@/constants/colors'
import { Ionicons } from '@expo/vector-icons'
import { useState, useEffect } from 'react'
import { Navbar } from '../../components/Navbar'
import { styles } from '../../assets/styles/home.styles'
import { useProducts } from '../../hooks/useProducts'
import { useCategories } from '../../hooks/useCategories'
import { API_URL } from '@/constants/api'

export default function Page() {
  const { user } = useUser();
  const router = useRouter();
  const [searchQuery, setSearchQuery] = useState('');

  const { products, isLoading: productsLoading, getFeaturedProducts } = useProducts();
  const { categories, isLoading: categoriesLoading } = useCategories();

  const featuredProducts = getFeaturedProducts();
  const isLoading = productsLoading || categoriesLoading;

  return (
    <View style={styles.container}>
      <Navbar
        user={user}
        searchQuery={searchQuery}
        setSearchQuery={setSearchQuery}
        router={router}
      />

      <ScrollView style={styles.mainContent}>
        <View style={styles.heroSection}>
          <Text style={styles.heroTitle}>{user?.firstName} Hoş Geldiniz</Text>
          <Text style={styles.heroSubtitle}>
            Ürünler, açık artırmalar, çekilişler ve takas fırsatlarını keşfedin
          </Text>
        </View>
        <View>
          <Text style={styles.sectionTitle}>Kayan banner olacak her 5 saniyede bir</Text>
              <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.categoriesScroll}>
            {['açık artırmalar', 'çekilişler', 'takaslar', ].map((category, index) => (
              <TouchableOpacity key={index} style={styles.categoryCard}>
                <Ionicons name="cube-outline" size={24} color={COLORS.primary} />
                <Text style={styles.categoryText}>{category}</Text>
              </TouchableOpacity>
              ))}
          </ScrollView>
        </View>

        <View style={styles.categoriesSection}>
          <Text style={styles.sectionTitle}>Kategoriler</Text>
          {categoriesLoading ? (
            <Text style={styles.loadingText}>Kategoriler yükleniyor...</Text>
          ) : (
            <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.categoriesScroll}>
              {categories.map((category) => (
                <TouchableOpacity key={category.id} style={styles.categoryCard}>
                  <Ionicons name="cube-outline" size={24} color={COLORS.primary} />
                  <Text style={styles.categoryText}>{category.name}</Text>
                </TouchableOpacity>
              ))}
            </ScrollView>
          )}
        </View>

        <View style={styles.featuredSection}>
          <Text style={styles.sectionTitle}>Öne Çıkan Ürünler</Text>
          {productsLoading ? (
            <Text style={styles.loadingText}>Ürünler yükleniyor...</Text>
          ) : featuredProducts.length > 0 ? (
            <View style={styles.productGrid}>
              {featuredProducts.slice(0, 4).map((product) => (
                <TouchableOpacity key={product.id} style={styles.productCard}>
                  <View style={styles.productImage}>
                    <Ionicons name="image-outline" size={40} color={COLORS.textLight} />
                  </View>
                  <Text style={styles.productName}>{product.name}</Text>
                  <Text style={styles.productPrice}>₺{product.price.toFixed(2)}</Text>
                </TouchableOpacity>
              ))}
            </View>
          ) : (
            <Text style={styles.emptyText}>Henüz öne çıkan ürün bulunmuyor</Text>
          )}
        </View>
      </ScrollView>
    </View>
  );
}
