import { useUser } from '@clerk/clerk-expo'
import { Text, View, ScrollView, TouchableOpacity } from 'react-native'
import { useRouter } from 'expo-router'
import { COLORS } from '@/constants/colors'
import { Ionicons } from '@expo/vector-icons'
import { useState } from 'react'
import { Navbar } from '../../components/Navbar'
import { styles } from '../../assets/styles/home.styles'

export default function Page() {
  const { user } = useUser();
  const router = useRouter();
  const [searchQuery, setSearchQuery] = useState('');

  return (
    <View style={styles.container}>
      <Navbar
        user={user}
        searchQuery={searchQuery}
        setSearchQuery={setSearchQuery}
        router={router}
      />

      {/* Ana İçerik */}
      <ScrollView style={styles.mainContent}>
        <View style={styles.heroSection}>
          <Text style={styles.heroTitle}>Hoş Geldiniz!</Text>
          <Text style={styles.heroSubtitle}>
            Ü<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> artırma, çekiliş ve takas fırsatların<PERSON> keşfedin
          </Text>
        </View>


        {/* Kategoriler */}
        <View style={styles.categoriesSection}>
          <Text style={styles.sectionTitle}>Kategoriler</Text>
          <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.categoriesScroll}>
            {['Elektronik', 'Giyim', 'Ev & Yaşam', 'Spor', 'Kitap', 'Oyuncak'].map((category, index) => (
              <TouchableOpacity key={index} style={styles.categoryCard}>
                <Ionicons name="cube-outline" size={24} color={COLORS.primary} />
                <Text style={styles.categoryText}>{category}</Text>
              </TouchableOpacity>
            ))}
          </ScrollView>
        </View>

        {/* Öne Çıkan Ürünler */}
        <View style={styles.featuredSection}>

