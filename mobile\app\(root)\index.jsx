import { useUser } from '@clerk/clerk-expo'
import { ScrollView, View, Alert, FlatList } from 'react-native'
import { useRouter } from 'expo-router'
import { useState } from 'react'
import { useCart } from '../../contexts/CartContext'
import { Box } from "@/components/ui/box"
import { Heading } from "@/components/ui/heading"
import { Text } from "@/components/ui/text"
import { VStack } from "@/components/ui/vstack"
import { HStack } from "@/components/ui/hstack"
import { Navbar } from '../../components/Navbar'
import { CategoryNavBar } from '../../components/CategoryNavBar'
import { BannerCarousel } from '../../components/BannerCarousel'
import { ProductCard } from '../../components/ProductCardNew'
import { useProducts } from '../../hooks/useProducts'
import { Categories } from '../../components/Categories'




export default function Page() {
  const { user } = useUser();
  const { addToCart } = useCart();
  const router = useRouter();
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState(null);
  const [selectedSubcategory, setSelectedSubcategory] = useState(null);

  const { isLoading: productsLoading, getFeaturedProducts } = useProducts();

  const featuredProducts = getFeaturedProducts();

  const handleCategorySelect = (category) => {
    setSelectedCategory(category);
    setSelectedSubcategory(null);
  };

  const handleSubcategorySelect = (subcategory) => {
    setSelectedSubcategory(subcategory);
  };

  const handleProductPress = (product) => {
    router.push(`/product/${product.id}`);
  };

  const handleAddToCart = (product) => {
    addToCart(product, 1);
    Alert.alert('Sepete Eklendi', `${product.name} sepete eklendi`);
  };

  const handleAddToWishlist = (product) => {
    Alert.alert('Favorilere Eklendi', `${product.name} favorilere eklendi`);
  };

  return (
    <View style={styles.container}>
      <Navbar
        user={user}
        searchQuery={searchQuery}
        setSearchQuery={setSearchQuery}
        router={router}
      />

      {/* Category Navigation */}
      <CategoryNavBar
        onCategorySelect={handleCategorySelect}
        onSubcategorySelect={handleSubcategorySelect}
      />

      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* Banner Carousel */}
        <BannerCarousel />

        {/* Featured Products Section */}
        
        <Box className="py-6">
          <Heading size="xl" className="mb-4 text-gray-900 px-4">
            ⭐ Öne Çıkan Ürünler
          </Heading>

          {productsLoading ? (
            <Box className="py-8 items-center">
              <Text className="text-gray-500">Ürünler yükleniyor...</Text>
            </Box>
          ) : featuredProducts.length > 0 ? (
            <ScrollView
              horizontal
              showsHorizontalScrollIndicator={false}
              contentContainerStyle={styles.horizontalScroll}
              style={styles.horizontalScrollView}
              contentContainerClassName="gap-2 bg-gray-300 max-w-[960px] mx-auto w-full"
              collumWrapperClassName="gap-2"
            >
              {featuredProducts.map((product) => (
                <ProductCard
                  key={product.id}
                  product={product}
                  onPress={handleProductPress}
                  onAddToCart={handleAddToCart}
                  onAddToWishlist={handleAddToWishlist}
                />
              ))}
            </ScrollView>
          ) : (
            <Box className="py-8 items-center px-4">
              <Text className="text-gray-500">Henüz öne çıkan ürün bulunmuyor</Text>
            </Box>
          )}
        </Box>
        <Box className="px-4 py-6 bg-gray-100">
          <Heading tyle={styles.categoryGrid} horizontal showsHorizontalScrollIndicator={false} size="xl" className="mb-4 text-gray-900">
            🛍️ Kategoriler
          </Heading>
          <Categories s 
            selectedCategory={selectedCategory}
            setSelectedCategory={setSelectedCategory}
          />
        </Box>  

        {/* Auctions Section */}
        <Box className="px-4 py-6 bg-blue-50">
          <Heading size="xl" className="mb-4 text-gray-900">
            🔨 Açık Artırmalar
          </Heading>

              <Box className="bg-white p-6 rounded-xl border border-gray-200">
              <Text className="text-center text-gray-600 text-lg">
                Açık artırma sistemi yakında aktif olacak!
              </Text>
              <Text className="text-center text-gray-500 mt-2">
                Nadir bulunan ürünler için teklif verin
              </Text>
            </Box>

          <Box className="bg-white p-6 rounded-xl border border-gray-200">
            <Text className="text-center text-gray-600 text-lg">
              Açık artırma sistemi yakında aktif olacak!
            </Text>
            <Text className="text-center text-gray-500 mt-2">
              Nadir bulunan ürünler için teklif verin
            </Text>
          </Box>
        </Box>
        {/* Lottery Section */}
        <Box className="px-4 py-6">
          <Heading size="xl" className="mb-4 text-gray-900">
            🎲 Çekilişler
          </Heading>
          <Box className="bg-gradient-to-r from-purple-500 to-pink-500 p-6 rounded-xl">
            <Text className="text-white text-xl font-bold mb-2">
              Büyük Çekiliş!
            </Text>
            <Text className="text-white/90 mb-4">
              iPhone 15 Pro kazanma şansı
            </Text>
            <Box className="bg-white/20 px-4 py-2 rounded-lg self-start">
              <Text className="text-white font-semibold">
                Yakında başlıyor
              </Text>
            </Box>
          </Box>
        </Box>
        {/* Exchange Section */}
        <Box className="px-4 py-6 bg-green-50">
          <Heading size="xl" className="mb-4 text-gray-900">
            🔄 Takas Merkezi
          </Heading>
          <Box className="bg-white p-6 rounded-xl border border-gray-200">
            <Text className="text-center text-gray-600 text-lg">
              Takas sistemi geliştiriliyor!
            </Text>
            <Text className="text-center text-gray-500 mt-2">
              Ürünlerinizi başka ürünlerle takas edin
            </Text>
          </Box>
        </Box>

        {/* Footer */}
        <Box className="px-4 py-8 bg-gray-900">
          <VStack className="space-y-4">
            <Heading size="lg" className="text-white text-center">
              Robinhood Marketplace
            </Heading>
            <Text className="text-gray-400 text-center">
              Güvenli alışveriş, açık artırma ve çekiliş platformu
            </Text>
            <HStack className="justify-center space-x-6 mt-4">
              <Text className="text-gray-400">Hakkımızda</Text>
              <Text className="text-gray-400">İletişim</Text>
              <Text className="text-gray-400">Yardım</Text>
            </HStack>
            <Text className="text-gray-500 text-center text-sm mt-4">
              © 2024 Robinhood. Tüm hakları saklıdır.
            </Text>
          </VStack>
        </Box>
      </ScrollView>
    </View>
  );
}

const styles = {
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  scrollView: {
    flex: 1,
  },
  horizontalScrollView: {
    paddingLeft: 16,
  },
  horizontalScroll: {
    paddingRight: 16,
  },
};


