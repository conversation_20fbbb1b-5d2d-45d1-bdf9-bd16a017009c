import { useUser } from '@clerk/clerk-expo'
import { Text, View, ScrollView, TouchableOpacity } from 'react-native'
import { useRouter } from 'expo-router'
import { COLORS } from '@/constants/colors'
import { Ionicons } from '@expo/vector-icons'
import { useState } from 'react'
import { Navbar } from '../../components/Navbar'
import { styles } from '../../assets/styles/home.styles'
import { useProducts } from '../../hooks/useProducts'
import { useCategories } from '../../hooks/useCategories'
import { ProductCard } from '../../components/ProductCard'



export default function Page() {
  const { user } = useUser();
  const router = useRouter();
  const [searchQuery, setSearchQuery] = useState('');

  const { isLoading: productsLoading, getFeaturedProducts } = useProducts();
  const { categories, isLoading: categoriesLoading } = useCategories();

  const featuredProducts = getFeaturedProducts();



  return (
    <View style={styles.container}>
      <Navbar
        user={user}
        searchQuery={searchQuery}
        setSearchQuery={setSearchQuery}
        router={router}
      />
      <ScrollView style={styles.mainContent}>
        <View style={styles.heroSection}>
          <Text style={styles.heroTitle}>{user?.firstName} Hoş Geldiniz</Text>
          <Text style={styles.heroSubtitle}>
            Ürünler, açık artırma, çekiliş ve takas fırsatlarını keşfedin
          </Text>
        </View>
        <View style={styles.categoriesSection}>
          <Text style={styles.sectionTitle}>Kategoriler</Text>
          {categoriesLoading ? (
            <Text style={styles.loadingText}>Kategoriler yükleniyor...</Text>
          ) : (
            <ScrollView
              horizontal
              showsHorizontalScrollIndicator={false}
              style={styles.categoriesScroll}
            >
              {categories.map((category) => (
                <TouchableOpacity
                  key={category.id}
                  style={styles.categoryCard}
                  onPress={() => router.push(`/category/${category.id}`)}
                >
                  <Ionicons name="pricetags" size={24} color={COLORS.text} />
                  <Text style={styles.categoryText}>{category.name}</Text>
                </TouchableOpacity>
              ))}
            </ScrollView>
          )}
        </View>
        <View style={styles.featuredSection}>
          <Text style={styles.sectionTitle}>Öne Çıkanlar</Text>
          {productsLoading ? (
            <Text style={styles.loadingText}>Ürünler yükleniyor...</Text>
          ) : (
            <View style={styles.productGrid}>
              {featuredProducts.map((product) => (
                <ProductCard key={product.id} product={product} />
              ))}
            </View>
          )}
        </View>
      </ScrollView>
    </View>




  );
} 


