{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../src/index.ts"], "names": [], "mappings": "AAAA,cAAc,eAAe,CAAC;AAC9B,cAAc,oBAAoB,CAAC;AACnC,cAAc,eAAe,CAAC;AAC9B,cAAc,UAAU,CAAC;AACzB,cAAc,SAAS,CAAC;AACxB,cAAc,aAAa,CAAC;AAC5B,cAAc,gBAAgB,CAAC;AAE/B,QAAQ;AACR,cAAc,qBAAqB,CAAC;AACpC,cAAc,qBAAqB,CAAC;AACpC,cAAc,sBAAsB,CAAC;AACrC,cAAc,4BAA4B,CAAC", "sourcesContent": ["export * from './AuthRequest';\nexport * from './AuthRequestHooks';\nexport * from './AuthSession';\nexport * from './Errors';\nexport * from './Fetch';\nexport * from './Discovery';\nexport * from './TokenRequest';\n\n// Types\nexport * from './AuthRequest.types';\nexport * from './AuthSession.types';\nexport * from './TokenRequest.types';\nexport * from './providers/Provider.types';\n\n// Provider specific types\nexport { GoogleAuthRequestConfig } from './providers/Google';\nexport { FacebookAuthRequestConfig } from './providers/Facebook';\n"]}