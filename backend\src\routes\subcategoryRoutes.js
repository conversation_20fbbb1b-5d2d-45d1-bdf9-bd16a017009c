import express from "express";
import { sql } from "../config/db.js";

const router = express.Router();

// Get all subcategories
router.get('/', async (req, res) => {
    try {
        const subcategories = await sql`
            SELECT s.*, c.name as category_name 
            FROM subcategories s
            LEFT JOIN categories c ON s.category_id = c.id
            ORDER BY c.name, s.name
        `;
        res.status(200).json(subcategories);
    } catch (error) {
        console.error('Error fetching subcategories:', error);
        res.status(500).json({ message: "Internal server error" });
    }
});

// Get subcategories by category ID
router.get('/category/:categoryId', async (req, res) => {
    try {
        const { categoryId } = req.params;
        
        if (isNaN(parseInt(categoryId))) {
            return res.status(400).json({ message: "Invalid category ID" });
        }
        
        const subcategories = await sql`
            SELECT * FROM subcategories 
            WHERE category_id = ${categoryId}
            ORDER BY name
        `;
        
        res.status(200).json(subcategories);
    } catch (error) {
        console.error('Error fetching subcategories by category:', error);
        res.status(500).json({ message: "Internal server error" });
    }
});

// Get subcategory by ID
router.get('/:id', async (req, res) => {
    try {
        const { id } = req.params;
        
        if (isNaN(parseInt(id))) {
            return res.status(400).json({ message: "Invalid subcategory ID" });
        }
        
        const subcategory = await sql`
            SELECT s.*, c.name as category_name 
            FROM subcategories s
            LEFT JOIN categories c ON s.category_id = c.id
            WHERE s.id = ${id}
        `;
        
        if (subcategory.length === 0) {
            return res.status(404).json({ message: "Subcategory not found" });
        }
        
        res.status(200).json(subcategory[0]);
    } catch (error) {
        console.error('Error fetching subcategory:', error);
        res.status(500).json({ message: "Internal server error" });
    }
});

// Create new subcategory
router.post('/', async (req, res) => {
    try {
        const { name, category_id } = req.body;

        // Validation
        if (!name || !category_id) {
            return res.status(400).json({ message: 'Name and category_id are required' });
        }

        // Check if category exists
        const categoryExists = await sql`
            SELECT id FROM categories WHERE id = ${category_id}
        `;

        if (categoryExists.length === 0) {
            return res.status(400).json({ message: 'Category not found' });
        }

        // Check if subcategory already exists in this category
        const existingSubcategory = await sql`
            SELECT * FROM subcategories WHERE name = ${name} AND category_id = ${category_id}
        `;

        if (existingSubcategory.length > 0) {
            return res.status(400).json({ message: 'Subcategory already exists in this category' });
        }

        const subcategory = await sql`
            INSERT INTO subcategories (name, category_id) 
            VALUES (${name}, ${category_id})
            RETURNING *
        `;

        res.status(201).json(subcategory[0]);
    } catch (error) {
        console.error('Error creating subcategory:', error);
        res.status(500).json({ message: "Internal server error" });
    }
});

// Update subcategory
router.put('/:id', async (req, res) => {
    try {
        const { id } = req.params;
        const { name, category_id } = req.body;

        if (isNaN(parseInt(id))) {
            return res.status(400).json({ message: "Invalid subcategory ID" });
        }

        if (!name || !category_id) {
            return res.status(400).json({ message: 'Name and category_id are required' });
        }

        // Check if subcategory exists
        const existingSubcategory = await sql`
            SELECT * FROM subcategories WHERE id = ${id}
        `;

        if (existingSubcategory.length === 0) {
            return res.status(404).json({ message: "Subcategory not found" });
        }

        // Check if category exists
        const categoryExists = await sql`
            SELECT id FROM categories WHERE id = ${category_id}
        `;

        if (categoryExists.length === 0) {
            return res.status(400).json({ message: 'Category not found' });
        }

        // Check if another subcategory with the same name exists in this category
        const duplicateSubcategory = await sql`
            SELECT * FROM subcategories 
            WHERE name = ${name} AND category_id = ${category_id} AND id != ${id}
        `;

        if (duplicateSubcategory.length > 0) {
            return res.status(400).json({ message: 'Subcategory name already exists in this category' });
        }

        const updatedSubcategory = await sql`
            UPDATE subcategories 
            SET name = ${name}, category_id = ${category_id}, updated_at = CURRENT_DATE
            WHERE id = ${id}
            RETURNING *
        `;

        res.status(200).json(updatedSubcategory[0]);
    } catch (error) {
        console.error('Error updating subcategory:', error);
        res.status(500).json({ message: "Internal server error" });
    }
});

// Delete subcategory
router.delete('/:id', async (req, res) => {
    try {
        const { id } = req.params;

        if (isNaN(parseInt(id))) {
            return res.status(400).json({ message: "Invalid subcategory ID" });
        }

        const result = await sql`
            DELETE FROM subcategories WHERE id = ${id} RETURNING *
        `;

        if (result.length === 0) {
            return res.status(404).json({ message: "Subcategory not found" });
        }

        res.status(200).json({ message: "Subcategory deleted successfully" });
    } catch (error) {
        console.error('Error deleting subcategory:', error);
        res.status(500).json({ message: "Internal server error" });
    }
});

export default router;
