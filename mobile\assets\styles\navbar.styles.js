import { StyleSheet } from 'react-native';
import { COLORS } from '../../constants/colors';

export const styles = StyleSheet.create({
  navbar: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 15,
    backgroundColor: COLORS.card,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.border,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 3,
    minHeight: 70,
    // Responsive design
    '@media (max-width: 768px)': {
      flexDirection: 'column',
      paddingVertical: 10,
      minHeight: 'auto',
    },
  },
  
  leftSection: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  
  logo: {
    width: 40,
    height: 40,
    marginRight: 10,
  },
  
  brandName: {
    fontSize: 20,
    fontWeight: 'bold',
    color: COLORS.primary,
  },
  
  centerSection: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 2,
    justifyContent: 'center',
    gap: 30,
    // Responsive design
    '@media (max-width: 768px)': {
      flex: 1,
      gap: 15,
      marginVertical: 10,
    },
    '@media (max-width: 480px)': {
      flexDirection: 'column',
      gap: 10,
    },
  },
  
  navLink: {
    paddingVertical: 8,
    paddingHorizontal: 12,
  },
  
  navLinkText: {
    fontSize: 16,
    color: COLORS.text,
    fontWeight: '500',
  },
  
  rightSection: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
    justifyContent: 'flex-end',
    gap: 15,
  },
  
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.background,
    borderRadius: 20,
    paddingHorizontal: 12,
    paddingVertical: 8,
    minWidth: 200,
    borderWidth: 1,
    borderColor: COLORS.border,
    // Responsive design
    '@media (max-width: 768px)': {
      minWidth: 150,
    },
    '@media (max-width: 480px)': {
      minWidth: '100%',
      marginVertical: 5,
    },
  },
  
  searchIcon: {
    marginRight: 8,
  },
  
  searchInput: {
    flex: 1,
    fontSize: 14,
    color: COLORS.text,
    outlineStyle: 'none', // Web için
  },
  
  userInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    paddingVertical: 8,
    paddingHorizontal: 12,
    backgroundColor: COLORS.background,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: COLORS.border,
  },
  
  userEmail: {
    fontSize: 14,
    color: COLORS.text,
    fontWeight: '500',
  },

  cartButton: {
    paddingVertical: 8,
    paddingHorizontal: 12,
  },

  cartIconContainer: {
    position: 'relative',
  },

  cartBadge: {
    position: 'absolute',
    top: -8,
    right: -8,
    backgroundColor: COLORS.error || '#FF4444',
    borderRadius: 10,
    minWidth: 20,
    height: 20,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 4,
  },

  cartBadgeText: {
    color: 'white',
    fontSize: 12,
    fontWeight: 'bold',
  },
});
