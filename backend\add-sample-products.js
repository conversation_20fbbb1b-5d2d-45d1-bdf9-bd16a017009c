import { sql } from './config/db.js';

async function addSampleProducts() {
    try {
        // <PERSON><PERSON><PERSON>ü<PERSON> ekle
        const sampleProducts = [
            {
                name: 'iPhone 15 Pro',
                description: 'Latest iPhone with advanced camera system',
                price: 999.99,
                original_price: 1099.99,
                discount_percentage: 9.09,
                images: ['https://example.com/iphone15.jpg'],
                category: 'Electronics',
                stock: 50,
                featured: true,
                status: 'active',
                seller_id: 'user_2example123',
                specifications: { storage: '256GB', color: 'Space Black' },
                tags: ['smartphone', 'apple', 'premium'],
                dynamic_pricing: {}
            },
            {
                name: 'Samsung Galaxy S24',
                description: 'Powerful Android smartphone with AI features',
                price: 799.99,
                original_price: 899.99,
                discount_percentage: 11.11,
                images: ['https://example.com/galaxy-s24.jpg'],
                category: 'Electronics',
                stock: 30,
                featured: true,
                status: 'active',
                seller_id: 'user_2example123',
                specifications: { storage: '128GB', color: 'Phantom Black' },
                tags: ['smartphone', 'samsung', 'android'],
                dynamic_pricing: {}
            },
            {
                name: 'MacBook Air M3',
                description: 'Ultra-thin laptop with M3 chip',
                price: 1299.99,
                original_price: 1399.99,
                discount_percentage: 7.14,
                images: ['https://example.com/macbook-air.jpg'],
                category: 'Computers',
                stock: 20,
                featured: false,
                status: 'active',
                seller_id: 'user_2example123',
                specifications: { ram: '8GB', storage: '256GB SSD' },
                tags: ['laptop', 'apple', 'ultrabook'],
                dynamic_pricing: {}
            },
            {
                name: 'Sony WH-1000XM5',
                description: 'Premium noise-canceling headphones',
                price: 349.99,
                original_price: 399.99,
                discount_percentage: 12.5,
                images: ['https://example.com/sony-headphones.jpg'],
                category: 'Audio',
                stock: 75,
                featured: false,
                status: 'active',
                seller_id: 'user_2example123',
                specifications: { type: 'Over-ear', wireless: true },
                tags: ['headphones', 'sony', 'noise-canceling'],
                dynamic_pricing: {}
            },
            {
                name: 'Nike Air Max 270',
                description: 'Comfortable running shoes with Air Max technology',
                price: 129.99,
                original_price: 149.99,
                discount_percentage: 13.33,
                images: ['https://example.com/nike-airmax.jpg'],
                category: 'Fashion',
                stock: 100,
                featured: true,
                status: 'active',
                seller_id: 'user_2example123',
                specifications: { size: '42', color: 'Black/White' },
                tags: ['shoes', 'nike', 'running'],
                dynamic_pricing: {}
            }
        ];

        for (const product of sampleProducts) {
            await sql`
                INSERT INTO products (
                    name, description, price, original_price, discount_percentage,
                    images, category, stock, featured, status, seller_id,
                    specifications, tags, dynamic_pricing
                ) VALUES (
                    ${product.name}, ${product.description}, ${product.price}, 
                    ${product.original_price}, ${product.discount_percentage},
                    ${JSON.stringify(product.images)}, ${product.category}, 
                    ${product.stock}, ${product.featured}, ${product.status}, 
                    ${product.seller_id}, ${JSON.stringify(product.specifications)}, 
                    ${JSON.stringify(product.tags)}, ${JSON.stringify(product.dynamic_pricing)}
                )
            `;
        }

        console.log('Sample products added successfully!');
        process.exit(0);
    } catch (error) {
        console.error('Error adding sample products:', error);
        process.exit(1);
    }
}

addSampleProducts();
