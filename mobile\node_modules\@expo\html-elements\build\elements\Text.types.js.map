{"version": 3, "file": "Text.types.js", "sourceRoot": "", "sources": ["../../src/elements/Text.types.ts"], "names": [], "mappings": "", "sourcesContent": ["import { TextProps } from '../primitives/Text';\nimport { ViewProps } from '../primitives/View';\n\nexport type QuoteProps = React.PropsWithChildren<TextProps & { cite?: string }>;\n\nexport type BlockQuoteProps = React.PropsWithChildren<ViewProps & { cite?: string }>;\n\nexport type TimeProps = React.PropsWithChildren<TextProps & { dateTime?: string }>;\n\nexport type LinkProps = React.PropsWithChildren<\n  TextProps & {\n    /** @platform web */\n    href?: string;\n    /** @platform web */\n    target?: string;\n    /** @platform web */\n    rel?: string;\n    /** @platform web */\n    download?: boolean | string;\n  }\n>;\n"]}