{"version": 3, "file": "Table.js", "sourceRoot": "", "sources": ["../../src/primitives/Table.ts"], "names": [], "mappings": "AAEA,OAAO,IAAI,MAAM,QAAQ,CAAC;AAW1B,MAAM,CAAC,MAAM,SAAS,GAAG,IAAqC,CAAC", "sourcesContent": ["import { ClassAttributes, ComponentProps, ComponentType } from 'react';\n\nimport Text from './Text';\n\ntype NativeTextProps = ComponentProps<typeof Text> & ClassAttributes<typeof Text>;\n\nexport type TableTextProps = NativeTextProps & {\n  /** @platform web */\n  colSpan?: number | string;\n  /** @platform web */\n  rowSpan?: number | string;\n};\n\nexport const TableText = Text as ComponentType<TableTextProps>;\n"]}