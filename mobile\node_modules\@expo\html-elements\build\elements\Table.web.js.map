{"version": 3, "file": "Table.web.js", "sourceRoot": "", "sources": ["../../src/elements/Table.web.tsx"], "names": [], "mappings": "AAAA,OAAO,KAAK,EAAE,EAAiB,UAAU,EAAE,MAAM,OAAO,CAAC;AAEzD,OAAO,IAAI,MAAM,uBAAuB,CAAC;AAGzC,SAAS,UAAU,CAAC,WAA8C;IAChE,OAAO,UAAU,CAAC,CAAC,KAAgB,EAAE,GAAG,EAAE,EAAE;QAC1C,OAAO,oBAAC,IAAI,OAAK,WAAW,KAAM,KAAK,EAAE,GAAG,EAAE,GAAG,GAAI,CAAC;IACxD,CAAC,CAA6B,CAAC;AACjC,CAAC;AAED,MAAM,CAAC,MAAM,KAAK,GAAG,UAAU,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,CAAC,CAAC;AACxD,KAAK,CAAC,WAAW,GAAG,OAAO,CAAC;AAE5B,MAAM,CAAC,MAAM,KAAK,GAAG,UAAU,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,CAAC,CAAC;AACxD,KAAK,CAAC,WAAW,GAAG,OAAO,CAAC;AAE5B,MAAM,CAAC,MAAM,KAAK,GAAG,UAAU,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,CAAC,CAAC;AACxD,KAAK,CAAC,WAAW,GAAG,OAAO,CAAC;AAE5B,MAAM,CAAC,MAAM,KAAK,GAAG,UAAU,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,CAAC,CAAC;AACxD,KAAK,CAAC,WAAW,GAAG,OAAO,CAAC;AAE5B,MAAM,CAAC,MAAM,EAAE,GAAG,UAAU,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;AAClD,EAAE,CAAC,WAAW,GAAG,IAAI,CAAC;AAEtB,MAAM,CAAC,MAAM,EAAE,GAAG,UAAU,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;AAClD,EAAE,CAAC,WAAW,GAAG,IAAI,CAAC;AAEtB,MAAM,CAAC,MAAM,EAAE,GAAG,UAAU,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;AAClD,EAAE,CAAC,WAAW,GAAG,IAAI,CAAC;AAEtB,MAAM,CAAC,MAAM,OAAO,GAAG,UAAU,CAAC,EAAE,SAAS,EAAE,SAAS,EAAE,CAAC,CAAC;AAC5D,OAAO,CAAC,WAAW,GAAG,SAAS,CAAC", "sourcesContent": ["import React, { ComponentType, forwardRef } from 'react';\n\nimport View from '../primitives/RNWView';\nimport { ViewProps } from '../primitives/View';\n\nfunction createView(nativeProps: ViewProps & { __element: string }): ComponentType<ViewProps> {\n  return forwardRef((props: ViewProps, ref) => {\n    return <View {...nativeProps} {...props} ref={ref} />;\n  }) as ComponentType<ViewProps>;\n}\n\nexport const Table = createView({ __element: 'table' });\nTable.displayName = 'Table';\n\nexport const THead = createView({ __element: 'thead' });\nTHead.displayName = 'THead';\n\nexport const TBody = createView({ __element: 'tbody' });\nTBody.displayName = 'TBody';\n\nexport const TFoot = createView({ __element: 'tfoot' });\nTFoot.displayName = 'TFoot';\n\nexport const TH = createView({ __element: 'th' });\nTH.displayName = 'TH';\n\nexport const TR = createView({ __element: 'tr' });\nTR.displayName = 'TR';\n\nexport const TD = createView({ __element: 'td' });\nTD.displayName = 'TD';\n\nexport const Caption = createView({ __element: 'caption' });\nCaption.displayName = 'Caption';\n"]}