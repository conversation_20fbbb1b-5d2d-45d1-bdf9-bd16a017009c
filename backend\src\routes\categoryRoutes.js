import express from "express";
import { sql } from "../config/db.js";

const router = express.Router();

// Get all categories
router.get('/', async (req, res) => {
    try {
        const categories = await sql`
            SELECT * FROM categories 
            ORDER BY created_at DESC
        `;
        res.status(200).json(categories);
    } catch (error) {
        console.error('Error fetching categories:', error);
        res.status(500).json({ message: "Internal server error" });
    }
});

// Get category by ID
router.get('/:id', async (req, res) => {
    try {
        const { id } = req.params;
        
        if (isNaN(parseInt(id))) {
            return res.status(400).json({ message: "Invalid category ID" });
        }
        
        const category = await sql`
            SELECT * FROM categories 
            WHERE id = ${id}
        `;
        
        if (category.length === 0) {
            return res.status(404).json({ message: "Category not found" });
        }
        
        res.status(200).json(category[0]);
    } catch (error) {
        console.error('Error fetching category:', error);
        res.status(500).json({ message: "Internal server error" });
    }
});

// Create new category
router.post('/', async (req, res) => {
    try {
        const { name } = req.body;

        // Validation
        if (!name) {
            return res.status(400).json({ message: 'Category name is required' });
        }

        // Check if category already exists
        const existingCategory = await sql`
            SELECT * FROM categories WHERE name = ${name}
        `;

        if (existingCategory.length > 0) {
            return res.status(400).json({ message: 'Category already exists' });
        }

        const category = await sql`
            INSERT INTO categories (name) 
            VALUES (${name})
            RETURNING *
        `;

        res.status(201).json(category[0]);
    } catch (error) {
        console.error('Error creating category:', error);
        res.status(500).json({ message: "Internal server error" });
    }
});

// Update category
router.put('/:id', async (req, res) => {
    try {
        const { id } = req.params;
        const { name } = req.body;

        if (isNaN(parseInt(id))) {
            return res.status(400).json({ message: "Invalid category ID" });
        }

        if (!name) {
            return res.status(400).json({ message: 'Category name is required' });
        }

        // Check if category exists
        const existingCategory = await sql`
            SELECT * FROM categories WHERE id = ${id}
        `;

        if (existingCategory.length === 0) {
            return res.status(404).json({ message: "Category not found" });
        }

        // Check if another category with the same name exists
        const duplicateCategory = await sql`
            SELECT * FROM categories WHERE name = ${name} AND id != ${id}
        `;

        if (duplicateCategory.length > 0) {
            return res.status(400).json({ message: 'Category name already exists' });
        }

        const updatedCategory = await sql`
            UPDATE categories 
            SET name = ${name}, updated_at = CURRENT_DATE
            WHERE id = ${id}
            RETURNING *
        `;

        res.status(200).json(updatedCategory[0]);
    } catch (error) {
        console.error('Error updating category:', error);
        res.status(500).json({ message: "Internal server error" });
    }
});

// Delete category
router.delete('/:id', async (req, res) => {
    try {
        const { id } = req.params;

        if (isNaN(parseInt(id))) {
            return res.status(400).json({ message: "Invalid category ID" });
        }

        // Check if category has products
        const productsInCategory = await sql`
            SELECT COUNT(*) as count FROM products WHERE category = (
                SELECT name FROM categories WHERE id = ${id}
            )
        `;

        if (productsInCategory[0].count > 0) {
            return res.status(400).json({ 
                message: "Cannot delete category with existing products" 
            });
        }

        const result = await sql`
            DELETE FROM categories WHERE id = ${id} RETURNING *
        `;

        if (result.length === 0) {
            return res.status(404).json({ message: "Category not found" });
        }

        res.status(200).json({ message: "Category deleted successfully" });
    } catch (error) {
        console.error('Error deleting category:', error);
        res.status(500).json({ message: "Internal server error" });
    }
});

// Get products by category
router.get('/:id/products', async (req, res) => {
    try {
        const { id } = req.params;
        
        if (isNaN(parseInt(id))) {
            return res.status(400).json({ message: "Invalid category ID" });
        }

        // Get category name first
        const category = await sql`
            SELECT name FROM categories WHERE id = ${id}
        `;

        if (category.length === 0) {
            return res.status(404).json({ message: "Category not found" });
        }

        const products = await sql`
            SELECT * FROM products 
            WHERE category = ${category[0].name} AND status = 'active'
            ORDER BY created_at DESC
        `;
        
        res.status(200).json(products);
    } catch (error) {
        console.error('Error fetching products by category:', error);
        res.status(500).json({ message: "Internal server error" });
    }
});

export default router;
