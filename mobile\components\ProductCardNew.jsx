import React from 'react';
import { TouchableOpacity, View } from 'react-native';
import { Button, ButtonText } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Heading } from "@/components/ui/heading";
import { Image } from "@/components/ui/image";
import { Text } from "@/components/ui/text";
import { VStack } from "@/components/ui/vstack";
import { HStack } from "@/components/ui/hstack";
import { Ionicons } from '@expo/vector-icons';
import { COLORS } from '../constants/colors';




// Safe function to get product image
const getProductImage = (product) => {
  try {
    if (!product.images) {
      return 'https://via.placeholder.com/300x200/f0f0f0/999999?text=No+Image';
    }
    
    if (typeof product.images === 'string') {
      try {
        const parsedImages = JSON.parse(product.images);
        return Array.isArray(parsedImages) && parsedImages.length > 0 
          ? parsedImages[0] 
          : 'https://via.placeholder.com/300x200/f0f0f0/999999?text=No+Image';
      } catch (parseError) {
        return product.images.startsWith('http') 
          ? product.images 
          : 'https://via.placeholder.com/300x200/f0f0f0/999999?text=No+Image';
      }
    }
    
    if (Array.isArray(product.images) && product.images.length > 0) {
      return product.images[0];
    }
    
    return 'https://via.placeholder.com/300x200/f0f0f0/999999?text=No+Image';
  } catch (error) {
    console.warn('Error getting product image:', error);
    return 'https://via.placeholder.com/300x200/f0f0f0/999999?text=No+Image';
  }
};

export const ProductCard = ({ product, onPress, onAddToCart, onAddToWishlist }) => {
  const discountPercentage = product.discount_percentage || 
    (product.original_price > product.price
      ? Math.round(((product.original_price - product.price) / product.original_price) * 100)
      : 0);

  return (
    <TouchableOpacity 
      onPress={() => onPress && onPress(product)}
      style={styles.card}
    >
      <Card className="p-0 rounded-xl w-[260px] m-2 overflow-hidden shadow-sm">
        {/* Product Image */}
        <View style={styles.imageContainer}>
          <Image
            source={{
              uri: getProductImage(product),
            }}
            className="h-[180px] w-full"
            alt={product.name}
            style={{ resizeMode: 'cover' }}
          />
          
          {/* Discount Badge */}
          {discountPercentage > 0 && (
            <View style={styles.discountBadge}>
              <Text className="text-white text-xs font-bold">
                %{discountPercentage} İndirim
              </Text>
            </View>
          )}
          
          {/* Featured Badge */}
          {product.featured && (
            <View style={styles.featuredBadge}>
              <Text className="text-white text-xs font-bold">
                ⭐ Öne Çıkan
              </Text>
            </View>
          )}
          
          {/* Wishlist Button */}
          <TouchableOpacity 
            style={styles.wishlistButton}
            onPress={() => onAddToWishlist && onAddToWishlist(product)}
          >
            <Ionicons name="heart-outline" size={18} color={COLORS.primary} />
          </TouchableOpacity>
        </View>

        {/* Product Info */}
        <VStack className="p-3">
          <Text className="text-xs font-normal mb-1 text-gray-500">
            {product.category}
          </Text>
          
          <Heading size="sm" className="mb-2 text-gray-900" numberOfLines={2}>
            {product.name}
          </Heading>
          
          <Text size="xs" className="text-gray-600 mb-2" numberOfLines={2}>
            {product.description || 'Ürün açıklaması bulunmamaktadır.'}
          </Text>

          {/* Price Section */}
          <HStack className="items-center justify-between mb-3">
            <VStack>
              <Text className="text-lg font-bold text-green-600">
                ₺{product.price?.toFixed(2)}
              </Text>
              {product.original_price > product.price && (
                <Text className="text-sm text-gray-400 line-through">
                  ₺{product.original_price?.toFixed(2)}
                </Text>
              )}
            </VStack>
            
            {product.stock > 0 ? (
              <Text className="text-xs text-green-600 font-semibold">
                Stokta {product.stock}
              </Text>
            ) : (
              <Text className="text-xs text-red-500 font-semibold">
                Stokta Yok
              </Text>
            )}
          </HStack>

          {/* Action Buttons */}
          <HStack className="space-x-2">
            <Button
              size="sm"
              className="flex-1 bg-blue-600"
              onPress={() => onAddToCart && onAddToCart(product)}
              disabled={product.stock <= 0}
            >
              <ButtonText size="sm">Sepete Ekle</ButtonText>
            </Button>
            
            <Button
              size="sm"
              variant="outline"
              className="px-3"
              onPress={() => onPress && onPress(product)}
            >
              <ButtonText size="sm" className="text-blue-600">
                Detay
              </ButtonText>
            </Button>
          </HStack>
        </VStack>
      </Card>
    </TouchableOpacity>
  );
};

const styles = {
  card: {
    marginHorizontal: 4,
  },
  imageContainer: {
    position: 'relative',
  },
  discountBadge: {
    position: 'absolute',
    top: 8,
    left: 8,
    backgroundColor: '#EF4444',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
  },
  featuredBadge: {
    position: 'absolute',
    top: 8,
    right: 8,
    backgroundColor: '#F59E0B',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
  },
  wishlistButton: {
    position: 'absolute',
    bottom: 8,
    right: 8,
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    borderRadius: 20,
    padding: 8,
  },
};
