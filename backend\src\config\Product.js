/**
 * Product Entity (Domain Object)
 * <PERSON>ş mantığından bağımsız ürün varlığı
 */
import "dotenv/config";

const { ProductStatus } = require('../../shared/enums');
const { PRODUCT, VALIDATION } = require('../../shared/constants');

class Product {
  constructor({
    id = null,
    name,
    description,
    price,
    originalPrice = null,
    discountPercentage = 0,
    images = [],
    category,
    stock = 0,
    featured = false,
    status = ProductStatus.ACTIVE,
    sellerId,
    specifications = {},
    tags = [],
    dynamicPricing = null,
    createdAt = new Date(),
    updatedAt = new Date(),
    skipValidation = false
  }) {
    this.id = id;
    this.name = name;
    this.description = description;
    this.price = price;
    this.originalPrice = originalPrice;
    this.discountPercentage = discountPercentage;
    this.images = images;
    this.category = category;
    this.stock = stock;
    this.featured = featured;
    this.status = status;
    this.sellerId = sellerId;
    this.specifications = specifications;
    this.tags = tags;
    this.dynamicPricing = dynamicPricing;
    this.createdAt = createdAt;
    this.updatedAt = updatedAt;

    // Validation (skip for database entities)
    if (!skipValidation) {
      this.validate();
    }
  }

  /**
   * Entity validation
   */
  validate() {
    if (!this.name || this.name.length < VALIDATION.NAME_MIN_LENGTH) {
      throw new Error(`Product name must be at least ${VALIDATION.NAME_MIN_LENGTH} characters`);
    }

    if (!this.description || this.description.length > VALIDATION.DESCRIPTION_MAX_LENGTH) {
      throw new Error(`Description cannot exceed ${VALIDATION.DESCRIPTION_MAX_LENGTH} characters`);
    }

    if (this.price < PRODUCT.MIN_PRICE || this.price > PRODUCT.MAX_PRICE) {
      throw new Error(`Price must be between ${PRODUCT.MIN_PRICE} and ${PRODUCT.MAX_PRICE}`);
    }

    if (this.stock < PRODUCT.MIN_STOCK || this.stock > PRODUCT.MAX_STOCK) {
      throw new Error(`Stock must be between ${PRODUCT.MIN_STOCK} and ${PRODUCT.MAX_STOCK}`);
    }

    if (!Object.values(ProductStatus).includes(this.status)) {
      throw new Error('Invalid product status');
    }

    if (this.images.length > PRODUCT.MAX_IMAGES) {
      throw new Error(`Maximum ${PRODUCT.MAX_IMAGES} images allowed`);
    }
  }

  /**
   * Ürünün aktif olup olmadığını kontrol et
   */
  isActive() {
    return this.status === ProductStatus.ACTIVE;
  }

  /**
   * Ürünün stokta olup olmadığını kontrol et
   */
  isInStock() {
    return this.stock > 0;
  }

  /**
   * Ürünün satılabilir olup olmadığını kontrol et
   */
  isAvailable() {
    return this.isActive() && this.isInStock();
  }

  /**
   * Ürünün öne çıkan olup olmadığını kontrol et
   */
  isFeatured() {
    return this.featured;
  }

  /**
   * İndirim yüzdesini hesapla
   */
  calculateDiscountPercentage() {
    if (!this.originalPrice || this.originalPrice <= this.price) {
      return 0;
    }
    return Math.round(((this.originalPrice - this.price) / this.originalPrice) * 100);
  }

  /**
   * Stok güncelle
   */
  updateStock(newStock) {
    if (newStock < PRODUCT.MIN_STOCK || newStock > PRODUCT.MAX_STOCK) {
      throw new Error(`Stock must be between ${PRODUCT.MIN_STOCK} and ${PRODUCT.MAX_STOCK}`);
    }
    this.stock = newStock;
    this.updatedAt = new Date();

    // Stok biterse durumu güncelle
    if (this.stock === 0 && this.status === ProductStatus.ACTIVE) {
      this.status = ProductStatus.OUT_OF_STOCK;
    }
  }

  /**
   * Stok azalt
   */
  decreaseStock(quantity = 1) {
    if (quantity <= 0) {
      throw new Error('Quantity must be positive');
    }
    if (this.stock < quantity) {
      throw new Error('Insufficient stock');
    }
    this.updateStock(this.stock - quantity);
  }

  /**
   * Stok artır
   */
  increaseStock(quantity = 1) {
    if (quantity <= 0) {
      throw new Error('Quantity must be positive');
    }
    this.updateStock(this.stock + quantity);
  }

  /**
   * Fiyat güncelle
   */
  updatePrice(newPrice) {
    if (newPrice < PRODUCT.MIN_PRICE || newPrice > PRODUCT.MAX_PRICE) {
      throw new Error(`Price must be between ${PRODUCT.MIN_PRICE} and ${PRODUCT.MAX_PRICE}`);
    }
    this.price = newPrice;
    this.discountPercentage = this.calculateDiscountPercentage();
    this.updatedAt = new Date();
  }

  /**
   * Ürün durumunu güncelle
   */
  updateStatus(status) {
    if (!Object.values(ProductStatus).includes(status)) {
      throw new Error('Invalid product status');
    }
    this.status = status;
    this.updatedAt = new Date();
  }

  /**
   * Öne çıkan durumunu güncelle
   */
  updateFeaturedStatus(featured) {
    this.featured = Boolean(featured);
    this.updatedAt = new Date();
  }

  /**
   * Resim ekle
   */
  addImage(imageUrl) {
    if (this.images.length >= PRODUCT.MAX_IMAGES) {
      throw new Error(`Maximum ${PRODUCT.MAX_IMAGES} images allowed`);
    }
    this.images.push(imageUrl);
    this.updatedAt = new Date();
  }

  /**
   * Resim kaldır
   */
  removeImage(imageUrl) {
    this.images = this.images.filter(img => img !== imageUrl);
    this.updatedAt = new Date();
  }

  /**
   * Tag ekle
   */
  addTag(tag) {
    if (!this.tags.includes(tag)) {
      this.tags.push(tag);
      this.updatedAt = new Date();
    }
  }

  /**
   * Tag kaldır
   */
  removeTag(tag) {
    this.tags = this.tags.filter(t => t !== tag);
    this.updatedAt = new Date();
  }

  /**
   * Entity'yi plain object'e çevir
   */
  toJSON() {
    return {
      id: this.id ? this.id.toString() : null,
      _id: this.id ? this.id.toString() : null, // Frontend _id bekliyor
      name: this.name,
      description: this.description,
      price: this.price,
      originalPrice: this.originalPrice,
      discountPercentage: this.discountPercentage,
      images: this.images,
      category: this.category,
      stock: this.stock,
      featured: this.featured,
      status: this.status,
      isActive: this.status === 'active', // Backward compatibility
      sellerId: this.sellerId,
      specifications: this.specifications,
      tags: this.tags,
      dynamicPricing: this.dynamicPricing,
      createdAt: this.createdAt,
      updatedAt: this.updatedAt
    };
  }

  /**
   * Entity'yi database için hazırla
   */
  toPersistence() {
    const data = {
      name: this.name,
      description: this.description,
      price: this.price,
      originalPrice: this.originalPrice,
      discountPercentage: this.discountPercentage,
      images: this.images,
      category: this.category,
      stock: this.stock,
      featured: this.featured,
      status: this.status,
      sellerId: this.sellerId,
      specifications: this.specifications,
      tags: this.tags,
      dynamicPricing: this.dynamicPricing,
      createdAt: this.createdAt,
      updatedAt: this.updatedAt
    };

    // Only include _id if it exists (for updates)
    if (this.id) {
      data._id = this.id;
    }

    return data;
  }

  /**
   * Database'den entity oluştur
   */
  static fromPersistence(data) {
    return new Product({
      id: data._id || data.id,
      name: data.name,
      description: data.description,
      price: data.price,
      originalPrice: data.originalPrice,
      discountPercentage: data.discountPercentage,
      images: data.images,
      category: data.category,
      stock: data.stock,
      featured: data.featured,
      status: data.status,
      sellerId: data.sellerId,
      specifications: data.specifications,
      tags: data.tags,
      dynamicPricing: data.dynamicPricing,
      createdAt: data.createdAt,
      updatedAt: data.updatedAt,
      skipValidation: true // Database'den gelen veriler için validation'ı atla
    });
  }
}

module.exports = Product;
