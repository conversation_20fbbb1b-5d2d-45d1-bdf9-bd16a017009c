{"name": "expo-crypto", "version": "14.1.4", "description": "Provides cryptography primitives for Android, iOS and web.", "main": "build/Crypto.js", "types": "build/Crypto.d.ts", "sideEffects": false, "scripts": {"build": "expo-module build", "clean": "expo-module clean", "lint": "expo-module lint", "test": "expo-module test", "prepare": "expo-module prepare", "prepublishOnly": "expo-module prepublishOnly", "expo-module": "expo-module"}, "keywords": ["react-native", "react-native-web", "expo", "crypto", "ios", "android", "web", "native"], "repository": {"type": "git", "url": "https://github.com/expo/expo.git", "directory": "packages/expo-crypto"}, "bugs": {"url": "https://github.com/expo/expo/issues"}, "author": "650 Industries, Inc.", "license": "MIT", "homepage": "https://docs.expo.dev/versions/latest/sdk/crypto/", "jest": {"preset": "expo-module-scripts"}, "dependencies": {"base64-js": "^1.3.0"}, "devDependencies": {"expo-module-scripts": "^4.1.6"}, "peerDependencies": {"expo": "*"}, "gitHead": "84355076bc31a356aa3d23257f387f221885f53d"}