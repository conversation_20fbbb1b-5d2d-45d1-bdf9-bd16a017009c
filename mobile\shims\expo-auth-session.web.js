// Web shim for expo-auth-session
// This provides minimal compatibility for web platform

export const AuthRequest = class {
  constructor() {}
  async promptAsync() {
    return { type: 'success' };
  }
};

export const AuthSessionResult = {
  SUCCESS: 'success',
  CANCEL: 'cancel',
  DISMISS: 'dismiss',
  ERROR: 'error',
};

export const ResponseType = {
  Code: 'code',
  Token: 'token',
  IdToken: 'id_token',
};

export const Prompt = {
  Login: 'login',
  Consent: 'consent',
  SelectAccount: 'select_account',
  None: 'none',
};

export const CodeChallengeMethod = {
  Plain: 'plain',
  S256: 'S256',
};

export const TokenType = {
  Bearer: 'Bearer',
  MAC: 'MAC',
};

export const GrantType = {
  AuthorizationCode: 'authorization_code',
  ClientCredentials: 'client_credentials',
  RefreshToken: 'refresh_token',
  Implicit: 'implicit',
};

// Mock functions for web compatibility
export const makeRedirectUri = (options = {}) => {
  return window.location.origin + '/auth/callback';
};

export const startAsync = async (options) => {
  return { type: 'success' };
};

export const dismissAsync = async () => {
  return { type: 'dismiss' };
};

export const getRedirectUrl = () => {
  return window.location.origin + '/auth/callback';
};

export const AuthError = class extends Error {
  constructor(message, code) {
    super(message);
    this.code = code;
  }
};

export const TokenError = class extends Error {
  constructor(message, code) {
    super(message);
    this.code = code;
  }
};

export const revokeAsync = async () => {
  return { success: true };
};

export const refreshAsync = async () => {
  return { 
    accessToken: 'mock_access_token',
    refreshToken: 'mock_refresh_token',
    tokenType: 'Bearer',
  };
};

export const exchangeCodeAsync = async () => {
  return {
    accessToken: 'mock_access_token',
    refreshToken: 'mock_refresh_token',
    tokenType: 'Bearer',
  };
};

// Default export
export default {
  AuthRequest,
  AuthSessionResult,
  ResponseType,
  Prompt,
  CodeChallengeMethod,
  TokenType,
  GrantType,
  makeRedirectUri,
  startAsync,
  dismissAsync,
  getRedirectUrl,
  AuthError,
  TokenError,
  revokeAsync,
  refreshAsync,
  exchangeCodeAsync,
};
