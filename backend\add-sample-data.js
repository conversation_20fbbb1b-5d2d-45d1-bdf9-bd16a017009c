import { sql } from './src/config/db.js';

async function addSampleData() {
    try {
        console.log('Adding sample data...');

        // 1. Kategor<PERSON> ekle
        const categories = [
            'Elektronik',
            '<PERSON><PERSON><PERSON><PERSON>',
            '<PERSON><PERSON> <PERSON> Yaşam',
            '<PERSON><PERSON>',
            '<PERSON><PERSON>',
            'Oyuncak',
            'Otomotiv',
            'Sağlık & Güzellik'
        ];

        console.log('Adding categories...');
        for (const categoryName of categories) {
            try {
                await sql`
                    INSERT INTO categories (name) 
                    VALUES (${categoryName})
                `;
                console.log(`Added category: ${categoryName}`);
            } catch (error) {
                if (!error.message.includes('duplicate')) {
                    console.error(`Error adding category ${categoryName}:`, error);
                }
            }
        }

        // 2. <PERSON><PERSON><PERSON><PERSON>ılar ekle
        const users = [
            {
                name: 'Admin User',
                email: '<EMAIL>',
                username: 'admin',
                password: '$2b$10$rOvHPxfzO2XkMtAL9k8zKOQGQQQQQQQQQQQQQQQQQQQQQQQQQQ', // hashed 'admin123'
                role: 'admin',
                status: 'active',
                approval_status: 'approved',
                is_email_verified: true,
                can_bid: true,
                can_participate_in_lottery: true,
                profile: JSON.stringify({ bio: 'System Administrator' })
            },
            {
                name: 'Test User',
                email: '<EMAIL>',
                username: 'testuser',
                password: '$2b$10$rOvHPxfzO2XkMtAL9k8zKOQGQQQQQQQQQQQQQQQQQQQQQQQQQQ', // hashed 'test123'
                role: 'user',
                status: 'active',
                approval_status: 'approved',
                is_email_verified: true,
                can_bid: true,
                can_participate_in_lottery: true,
                profile: JSON.stringify({ bio: 'Test user account' })
            }
        ];

        console.log('Adding users...');
        for (const user of users) {
            try {
                await sql`
                    INSERT INTO users (
                        name, email, username, password, role, status, approval_status,
                        is_email_verified, can_bid, can_participate_in_lottery, profile
                    ) VALUES (
                        ${user.name}, ${user.email}, ${user.username}, ${user.password}, 
                        ${user.role}, ${user.status}, ${user.approval_status},
                        ${user.is_email_verified}, ${user.can_bid}, ${user.can_participate_in_lottery}, 
                        ${user.profile}
                    )
                `;
                console.log(`Added user: ${user.name}`);
            } catch (error) {
                if (!error.message.includes('duplicate')) {
                    console.error(`Error adding user ${user.name}:`, error);
                }
            }
        }

        // 3. Ürünler ekle
        const products = [
            {
                name: 'iPhone 15 Pro',
                description: 'En yeni iPhone modeli, 256GB depolama alanı ile',
                price: 45000,
                original_price: 50000,
                discount_percentage: 10,
                images: JSON.stringify(['iphone15pro.jpg']),
                category: 'Elektronik',
                stock: 10,
                featured: true,
                status: 'active',
                seller_id: '1',
                specifications: JSON.stringify({ storage: '256GB', color: 'Space Black' }),
                tags: JSON.stringify(['apple', 'iphone', 'smartphone']),
                dynamic_pricing: JSON.stringify({})
            },
            {
                name: 'Samsung Galaxy S24',
                description: 'Samsung\'ın en yeni flagship telefonu',
                price: 35000,
                original_price: 38000,
                discount_percentage: 8,
                images: JSON.stringify(['galaxy-s24.jpg']),
                category: 'Elektronik',
                stock: 15,
                featured: true,
                status: 'active',
                seller_id: '1',
                specifications: JSON.stringify({ storage: '128GB', color: 'Phantom Black' }),
                tags: JSON.stringify(['samsung', 'galaxy', 'android']),
                dynamic_pricing: JSON.stringify({})
            },
            {
                name: 'Nike Air Max 270',
                description: 'Rahat ve şık spor ayakkabı',
                price: 1200,
                original_price: 1500,
                discount_percentage: 20,
                images: JSON.stringify(['nike-air-max.jpg']),
                category: 'Spor',
                stock: 25,
                featured: false,
                status: 'active',
                seller_id: '2',
                specifications: JSON.stringify({ size: '42', color: 'White/Black' }),
                tags: JSON.stringify(['nike', 'shoes', 'sports']),
                dynamic_pricing: JSON.stringify({})
            },
            {
                name: 'Levi\'s 501 Jeans',
                description: 'Klasik Levi\'s jean pantolon',
                price: 800,
                original_price: 1000,
                discount_percentage: 20,
                images: JSON.stringify(['levis-501.jpg']),
                category: 'Giyim',
                stock: 30,
                featured: false,
                status: 'active',
                seller_id: '2',
                specifications: JSON.stringify({ size: 'L', color: 'Blue' }),
                tags: JSON.stringify(['levis', 'jeans', 'denim']),
                dynamic_pricing: JSON.stringify({})
            }
        ];

        console.log('Adding products...');
        for (const product of products) {
            try {
                await sql`
                    INSERT INTO products (
                        name, description, price, original_price, discount_percentage,
                        images, category, stock, featured, status, seller_id,
                        specifications, tags, dynamic_pricing
                    ) VALUES (
                        ${product.name}, ${product.description}, ${product.price}, 
                        ${product.original_price}, ${product.discount_percentage},
                        ${product.images}, ${product.category}, ${product.stock}, 
                        ${product.featured}, ${product.status}, ${product.seller_id},
                        ${product.specifications}, ${product.tags}, ${product.dynamic_pricing}
                    )
                `;
                console.log(`Added product: ${product.name}`);
            } catch (error) {
                if (!error.message.includes('duplicate')) {
                    console.error(`Error adding product ${product.name}:`, error);
                }
            }
        }

        console.log('Sample data added successfully!');
        process.exit(0);
    } catch (error) {
        console.error('Error adding sample data:', error);
        process.exit(1);
    }
}

addSampleData();
