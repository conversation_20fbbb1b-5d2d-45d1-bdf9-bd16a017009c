// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Converts basic link 1`] = `
"import { A } from "@expo/html-elements";import { jsx as _jsx } from "react/jsx-runtime";
function App() {
  return _jsx(A, { href: "#", children: "Link" });
}"
`;

exports[`Converts fixture 1`] = `
"import { Main, Article, P, Code, Div } from "@expo/html-elements";import { Image } from 'react-native';import { jsx as _jsx } from "react/jsx-runtime";import { jsxs as _jsxs } from "react/jsx-runtime";

export default function App() {
  return (
    _jsx(Main, { className: "flex-1 items-center justify-center", children:
      _jsxs(Article, { className: "md:flex bg-slate-100 rounded-xl p-8 md:p-0 dark:bg-slate-800", children: [
        _jsx(Image, {
          className: "w-24 h-24 md:w-48 md:h-auto md:rounded-none rounded-full mx-auto",
          source: {
            uri: 'https://en.gravatar.com/userimage/*********/078ee8361156d0e1c37b90e7851fed4b.png'
          },
          width: "384",
          height: "512" }),

        _jsxs(Main, { className: "pt-6 md:p-8 text-center md:text-left space-y-4", children: [
          _jsxs(P, { className: "text-lg font-medium", children: ["With ",
            _jsx(Code, { children: "@expo/html-elements" }), ", you can write HTML syntax that renders to real native components. Combined with Tailwind CSS, you have an experience that makes web developers feel at home.\\u201D"] }),



          _jsxs(Div, { className: "font-medium", children: [
            _jsx(P, { className: "text-sky-500 dark:text-sky-400 my-0", children: "Evan Bacon" }),
            _jsx(P, { className: "text-slate-700 dark:text-slate-500 my-2", children: "Engineer, Expo" })] })] })] }) }));





}"
`;

exports[`Doesn't support unknowns 1`] = `
"import { jsx as _jsx } from "react/jsx-runtime";
function App() {
  return _jsx("foobar", { href: "#", children: "Link" });
}"
`;

exports[`Skips injecting the import if one is already present 1`] = `
"import { A } from "@expo/html-elements";
import { A } from '@expo/html-elements';import { jsx as _jsx } from "react/jsx-runtime";
function App() {
  return _jsx(A, { href: "#", children: "Link" });
}"
`;
