import { ComponentType } from 'react';
import { TextProps } from '../primitives/Text';
import { BlockQuoteProps, QuoteProps, TimeProps } from './Text.types';
export declare const P: ComponentType<TextProps>;
export declare const B: ComponentType<TextProps>;
export declare const S: ComponentType<TextProps>;
export declare const Del: ComponentType<TextProps>;
export declare const Strong: ComponentType<TextProps>;
export declare const I: ComponentType<TextProps>;
export declare const Q: ComponentType<QuoteProps>;
export declare const BlockQuote: ComponentType<BlockQuoteProps>;
export declare const EM: ComponentType<TextProps>;
export declare const BR: ComponentType<TextProps>;
export declare const Small: ComponentType<TextProps>;
export declare const Mark: ComponentType<TextProps>;
export declare const Code: ComponentType<TextProps>;
export declare const Time: ComponentType<TimeProps>;
export declare const Pre: ComponentType<TextProps>;
//# sourceMappingURL=_Text.web.d.ts.map