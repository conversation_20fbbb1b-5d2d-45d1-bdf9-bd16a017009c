import React, { useState } from 'react';
import { View, Text, TextInput, TouchableOpacity, ScrollView, Alert } from 'react-native';
import { useUser } from '@clerk/clerk-expo';
import { useRouter } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import { COLORS } from '@/constants/colors';
import { API_URL } from '@/constants/api';
import { useCategories } from '../../../../hooks/useCategories';
import { Navbar } from '../../../../components/Navbar';
import { styles } from '../../../../assets/styles/form.styles';

export default function AddProduct() {
  const { user } = useUser();
  const router = useRouter();
  const { categories } = useCategories();
  const [searchQuery, setSearchQuery] = useState('');
  const [loading, setLoading] = useState(false);
  const [showCategoryDropdown, setShowCategoryDropdown] = useState(false);

  const [formData, setFormData] = useState({
    name: '',
    description: '',
    price: '',
    original_price: '',
    category: '',
    stock: '',
    featured: false,
    specifications: '',
    tags: '',
  });

  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSubmit = async () => {
    try {
      // Validation
      if (!formData.name || !formData.description || !formData.price || !formData.category) {
        Alert.alert('Hata', 'Lütfen tüm zorunlu alanları doldurun');
        return;
      }

      setLoading(true);

      const productData = {
        name: formData.name,
        description: formData.description,
        price: parseFloat(formData.price),
        original_price: formData.original_price ? parseFloat(formData.original_price) : parseFloat(formData.price),
        category: formData.category,
        stock: parseInt(formData.stock) || 0,
        featured: formData.featured,
        status: 'active',
        seller_id: user?.id || '1',
        specifications: formData.specifications ? JSON.stringify({ details: formData.specifications }) : JSON.stringify({}),
        tags: formData.tags ? JSON.stringify(formData.tags.split(',').map(tag => tag.trim())) : JSON.stringify([]),
        images: JSON.stringify(['placeholder.jpg']),
        dynamic_pricing: JSON.stringify({})
      };

      const response = await fetch(`${API_URL}/products`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(productData),
      });

      if (response.ok) {
        Alert.alert('Başarılı', 'Ürün başarıyla eklendi', [
          { text: 'Tamam', onPress: () => router.back() }
        ]);
      } else {
        const errorData = await response.json();
        Alert.alert('Hata', errorData.message || 'Ürün eklenirken bir hata oluştu');
      }
    } catch (error) {
      console.error('Error adding product:', error);
      Alert.alert('Hata', 'Ürün eklenirken bir hata oluştu');
    } finally {
      setLoading(false);
    }
  };

  return (
    <View style={styles.container}>
      <Navbar 
        user={user} 
        searchQuery={searchQuery} 
        setSearchQuery={setSearchQuery}
        router={router}
      />
      
      <ScrollView style={styles.formContainer}>
        <View style={styles.header}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => router.back()}
          >
            <Ionicons name="arrow-back" size={24} color={COLORS.text} />
          </TouchableOpacity>
          <Text style={styles.title}>Yeni Ürün Ekle</Text>
        </View>

        <View style={styles.form}>
          <View style={styles.inputGroup}>
            <Text style={styles.label}>Ürün Adı *</Text>
            <TextInput
              style={styles.input}
              value={formData.name}
              onChangeText={(value) => handleInputChange('name', value)}
              placeholder="Ürün adını girin"
              placeholderTextColor={COLORS.textLight}
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.label}>Açıklama *</Text>
            <TextInput
              style={[styles.input, styles.textArea]}
              value={formData.description}
              onChangeText={(value) => handleInputChange('description', value)}
              placeholder="Ürün açıklamasını girin"
              placeholderTextColor={COLORS.textLight}
              multiline
              numberOfLines={4}
            />
          </View>

          <View style={styles.row}>
            <View style={[styles.inputGroup, styles.halfWidth]}>
              <Text style={styles.label}>Fiyat *</Text>
              <TextInput
                style={styles.input}
                value={formData.price}
                onChangeText={(value) => handleInputChange('price', value)}
                placeholder="0.00"
                placeholderTextColor={COLORS.textLight}
                keyboardType="numeric"
              />
            </View>

            <View style={[styles.inputGroup, styles.halfWidth]}>
              <Text style={styles.label}>Orijinal Fiyat</Text>
              <TextInput
                style={styles.input}
                value={formData.original_price}
                onChangeText={(value) => handleInputChange('original_price', value)}
                placeholder="0.00"
                placeholderTextColor={COLORS.textLight}
                keyboardType="numeric"
              />
            </View>
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.label}>Kategori *</Text>
            <View style={styles.dropdownContainer}>
              <TouchableOpacity
                style={styles.dropdown}
                onPress={() => setShowCategoryDropdown(!showCategoryDropdown)}
              >
                <Text style={[styles.dropdownText, !formData.category && styles.dropdownPlaceholder]}>
                  {formData.category || 'Kategori seçin'}
                </Text>
                <Ionicons
                  name={showCategoryDropdown ? "chevron-up" : "chevron-down"}
                  size={20}
                  color={COLORS.textLight}
                />
              </TouchableOpacity>

              {showCategoryDropdown && (
                <View style={styles.dropdownList}>
                  {categories.map((category) => (
                    <TouchableOpacity
                      key={category.id}
                      style={styles.dropdownItem}
                      onPress={() => {
                        handleInputChange('category', category.name);
                        setShowCategoryDropdown(false);
                      }}
                    >
                      <Text style={styles.dropdownItemText}>{category.name}</Text>
                    </TouchableOpacity>
                  ))}
                </View>
              )}
            </View>
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.label}>Stok Miktarı</Text>
            <TextInput
              style={styles.input}
              value={formData.stock}
              onChangeText={(value) => handleInputChange('stock', value)}
              placeholder="0"
              placeholderTextColor={COLORS.textLight}
              keyboardType="numeric"
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.label}>Özellikler</Text>
            <TextInput
              style={[styles.input, styles.textArea]}
              value={formData.specifications}
              onChangeText={(value) => handleInputChange('specifications', value)}
              placeholder="Ürün özelliklerini girin"
              placeholderTextColor={COLORS.textLight}
              multiline
              numberOfLines={3}
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.label}>Etiketler (virgülle ayırın)</Text>
            <TextInput
              style={styles.input}
              value={formData.tags}
              onChangeText={(value) => handleInputChange('tags', value)}
              placeholder="etiket1, etiket2, etiket3"
              placeholderTextColor={COLORS.textLight}
            />
          </View>

          <View style={styles.checkboxGroup}>
            <TouchableOpacity
              style={styles.checkbox}
              onPress={() => handleInputChange('featured', !formData.featured)}
            >
              <Ionicons 
                name={formData.featured ? "checkbox" : "square-outline"} 
                size={24} 
                color={COLORS.primary} 
              />
              <Text style={styles.checkboxLabel}>Öne çıkan ürün</Text>
            </TouchableOpacity>
          </View>

          <TouchableOpacity
            style={[styles.submitButton, loading && styles.submitButtonDisabled]}
            onPress={handleSubmit}
            disabled={loading}
          >
            <Text style={styles.submitButtonText}>
              {loading ? 'Ekleniyor...' : 'Ürün Ekle'}
            </Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </View>
  );
}
