import React from 'react';
import { TableTextProps } from '../primitives/Table';
import { TextProps } from '../primitives/Text';
import { ViewProps } from '../primitives/View';
export declare const Table: React.ComponentType<ViewProps>;
export declare const THead: React.ComponentType<ViewProps>;
export declare const TBody: React.ComponentType<ViewProps>;
export declare const TFoot: React.ComponentType<ViewProps>;
export declare const TH: React.ComponentType<TableTextProps>;
export declare const TR: React.ComponentType<ViewProps>;
export declare const TD: React.ComponentType<TableTextProps>;
export declare const Caption: React.ComponentType<TextProps>;
//# sourceMappingURL=Table.d.ts.map