import { StyleSheet } from 'react-native';
import { COLORS } from '../../constants/colors';

export const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  
  dashboardContent: {
    flex: 1,
    flexDirection: 'row',
    // Responsive design
    '@media (max-width: 768px)': {
      flexDirection: 'column',
    },
  },
  
  sidebar: {
    width: 300,
    backgroundColor: COLORS.card,
    borderRightWidth: 1,
    borderRightColor: COLORS.border,
    padding: 20,
    // Responsive design
    '@media (max-width: 768px)': {
      width: '100%',
      borderRightWidth: 0,
      borderBottomWidth: 1,
      borderBottomColor: COLORS.border,
    },
  },
  
  sidebarTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: COLORS.text,
    marginBottom: 30,
  },
  
  sidebarItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 15,
    borderRadius: 10,
    marginBottom: 10,
    backgroundColor: 'transparent',
  },
  
  sidebarItemActive: {
    backgroundColor: COLORS.primary,
  },
  
  sidebarItemContent: {
    marginLeft: 15,
    flex: 1,
  },
  
  sidebarItemTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: COLORS.text,
    marginBottom: 4,
  },
  
  sidebarItemTitleActive: {
    color: COLORS.white,
  },
  
  sidebarItemDescription: {
    fontSize: 12,
    color: COLORS.textLight,
    lineHeight: 16,
  },
  
  sidebarItemDescriptionActive: {
    color: COLORS.white,
    opacity: 0.8,
  },
  
  mainContent: {
    flex: 1,
    padding: 30,
  },
  
  contentArea: {
    backgroundColor: COLORS.card,
    borderRadius: 15,
    padding: 30,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 3,
  },
  
  contentTitle: {
    fontSize: 28,
    fontWeight: 'bold',
    color: COLORS.text,
    marginBottom: 15,
  },
  
  contentDescription: {
    fontSize: 16,
    color: COLORS.textLight,
    lineHeight: 24,
    marginBottom: 30,
  },
  
  actionButtons: {
    flexDirection: 'row',
    gap: 15,
  },
  
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.primary,
    paddingVertical: 15,
    paddingHorizontal: 20,
    borderRadius: 10,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 3,
  },
  
  secondaryButton: {
    backgroundColor: 'transparent',
    borderWidth: 2,
    borderColor: COLORS.primary,
  },
  
  actionButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: COLORS.white,
    marginLeft: 8,
  },
  
  secondaryButtonText: {
    color: COLORS.primary,
  },

  // List page styles
  pageContent: {
    flex: 1,
    padding: 20,
  },

  pageHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 20,
    paddingBottom: 15,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.border,
  },

  backButton: {
    padding: 10,
    borderRadius: 8,
    backgroundColor: COLORS.card,
  },

  pageTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: COLORS.text,
    flex: 1,
    textAlign: 'center',
  },

  addButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.primary,
    paddingVertical: 10,
    paddingHorizontal: 15,
    borderRadius: 8,
  },

  addButtonText: {
    color: COLORS.white,
    fontWeight: '600',
    marginLeft: 5,
  },

  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },

  loadingText: {
    fontSize: 16,
    color: COLORS.textLight,
  },

  list: {
    flex: 1,
  },

  listContent: {
    paddingBottom: 20,
  },

  listItem: {
    flexDirection: 'row',
    backgroundColor: COLORS.card,
    borderRadius: 10,
    padding: 15,
    marginBottom: 10,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },

  listItemContent: {
    flex: 1,
  },

  listItemTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: COLORS.text,
    marginBottom: 5,
  },

  listItemSubtitle: {
    fontSize: 14,
    color: COLORS.textLight,
    marginBottom: 5,
  },

  listItemPrice: {
    fontSize: 16,
    fontWeight: 'bold',
    color: COLORS.primary,
    marginBottom: 5,
  },

  listItemDescription: {
    fontSize: 14,
    color: COLORS.textLight,
    lineHeight: 20,
  },

  listItemActions: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 10,
  },

  editButton: {
    backgroundColor: COLORS.warning || '#f39c12',
  },

  deleteButton: {
    backgroundColor: COLORS.danger || '#e74c3c',
  },

  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 50,
  },

  emptyText: {
    fontSize: 18,
    color: COLORS.textLight,
    marginVertical: 20,
    textAlign: 'center',
  },

  emptyButton: {
    backgroundColor: COLORS.primary,
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 8,
  },

  emptyButtonText: {
    color: COLORS.white,
    fontWeight: '600',
  },

  // User badges
  userBadges: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 5,
    marginVertical: 5,
  },

  badge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    alignSelf: 'flex-start',
  },

  badgeText: {
    color: COLORS.white,
    fontSize: 10,
    fontWeight: 'bold',
  },
});
