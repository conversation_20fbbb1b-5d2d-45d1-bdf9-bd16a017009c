{"version": 3, "file": "TokenRequest.d.ts", "sourceRoot": "", "sources": ["../src/TokenRequest.ts"], "names": [], "mappings": "AAIA,OAAO,KAAK,aAAa,MAAM,aAAa,CAAC;AAE7C,OAAO,EAAE,OAAO,EAAgB,MAAM,SAAS,CAAC;AAChD,OAAO,EACL,wBAAwB,EACxB,SAAS,EACT,yBAAyB,EACzB,wBAAwB,EAExB,kBAAkB,EAClB,mBAAmB,EACnB,SAAS,EACT,aAAa,EACd,MAAM,sBAAsB,CAAC;AAE9B;;GAEG;AACH,wBAAgB,uBAAuB,IAAI,MAAM,CAEhD;AAED;;;;GAIG;AACH,qBAAa,aAAc,YAAW,mBAAmB;IACvD;;;;;OAKG;IACH,MAAM,CAAC,YAAY,CACjB,KAAK,EAAE,IAAI,CAAC,aAAa,EAAE,WAAW,GAAG,UAAU,CAAC;IACpD;;OAEG;IACH,aAAa,GAAE,MAAqB,GACnC,OAAO;IAWV;;;;OAIG;IACH,MAAM,CAAC,eAAe,CAAC,MAAM,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,GAAG,aAAa;IAalE,WAAW,EAAE,MAAM,CAAC;IACpB,SAAS,EAAE,SAAS,CAAC;IACrB,SAAS,CAAC,EAAE,MAAM,CAAC;IACnB,YAAY,CAAC,EAAE,MAAM,CAAC;IACtB,KAAK,CAAC,EAAE,MAAM,CAAC;IACf,KAAK,CAAC,EAAE,MAAM,CAAC;IACf,OAAO,CAAC,EAAE,MAAM,CAAC;IACjB,QAAQ,EAAE,MAAM,CAAC;gBAEL,QAAQ,EAAE,mBAAmB;IAWzC,OAAO,CAAC,mBAAmB;IAW3B,gBAAgB,IAAI,mBAAmB;IAajC,YAAY,CAChB,MAAM,EAAE,IAAI,CAAC,kBAAkB,EAAE,WAAW,GAAG,cAAc,CAAC,EAC9D,SAAS,EAAE,IAAI,CAAC,aAAa,CAAC,iBAAiB,EAAE,eAAe,CAAC,GAChE,OAAO,CAAC,aAAa,CAAC;IAazB,aAAa,IAAI,OAAO;CAIzB;AAED,qBAAa,OAAO,CAAC,CAAC,EAAE,CAAC;IACX,SAAS,CAAC,OAAO,EAAE,CAAC;gBAAV,OAAO,EAAE,CAAC;IAE1B,YAAY,CAAC,SAAS,EAAE,aAAa,CAAC,iBAAiB,GAAG,OAAO,CAAC,CAAC,CAAC;IAI1E,gBAAgB,IAAI,CAAC;IAIrB,YAAY,IAAI,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC;CAGvC;AAED;;GAEG;AACH,qBAAa,YAAY,CAAC,CAAC,SAAS,kBAAkB,CACpD,SAAQ,OAAO,CAAC,CAAC,EAAE,aAAa,CAChC,YAAW,kBAAkB;IASpB,SAAS,EAAE,SAAS;IAP7B,QAAQ,CAAC,QAAQ,EAAE,MAAM,CAAC;IAC1B,QAAQ,CAAC,YAAY,CAAC,EAAE,MAAM,CAAC;IAC/B,QAAQ,CAAC,MAAM,CAAC,EAAE,MAAM,EAAE,CAAC;IAC3B,QAAQ,CAAC,WAAW,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;gBAG5C,OAAO,EAAE,CAAC,EACH,SAAS,EAAE,SAAS;IAS7B,UAAU,IAAI,OAAO;IAef,YAAY,CAAC,SAAS,EAAE,IAAI,CAAC,aAAa,CAAC,iBAAiB,EAAE,eAAe,CAAC;IA+BpF,YAAY;CAuBb;AAED;;;;GAIG;AACH,qBAAa,kBACX,SAAQ,YAAY,CAAC,wBAAwB,CAC7C,YAAW,wBAAwB;IAEnC,QAAQ,CAAC,IAAI,EAAE,MAAM,CAAC;IACtB,QAAQ,CAAC,WAAW,EAAE,MAAM,CAAC;gBAEjB,OAAO,EAAE,wBAAwB;IAoB7C,YAAY;IAcZ,gBAAgB;;;;;;;;;CAWjB;AAED;;;;GAIG;AACH,qBAAa,mBACX,SAAQ,YAAY,CAAC,yBAAyB,CAC9C,YAAW,yBAAyB;IAEpC,QAAQ,CAAC,YAAY,CAAC,EAAE,MAAM,CAAC;gBAEnB,OAAO,EAAE,yBAAyB;IAM9C,YAAY;IAUZ,gBAAgB;;;;;;;;CAUjB;AAED;;;;GAIG;AACH,qBAAa,kBACX,SAAQ,OAAO,CAAC,wBAAwB,EAAE,OAAO,CACjD,YAAW,wBAAwB;IAEnC,QAAQ,CAAC,QAAQ,CAAC,EAAE,MAAM,CAAC;IAC3B,QAAQ,CAAC,YAAY,CAAC,EAAE,MAAM,CAAC;IAC/B,QAAQ,CAAC,KAAK,EAAE,MAAM,CAAC;IACvB,QAAQ,CAAC,aAAa,CAAC,EAAE,aAAa,CAAC;gBAE3B,OAAO,EAAE,wBAAwB;IAS7C,UAAU,IAAI,OAAO;IAerB;;;;OAIG;IACG,YAAY,CAAC,SAAS,EAAE,IAAI,CAAC,aAAa,CAAC,iBAAiB,EAAE,oBAAoB,CAAC;IAczF,gBAAgB;;;;;;IAShB,YAAY,IAAI,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC;CAcvC;AAGD;;;;;;GAMG;AACH,wBAAgB,iBAAiB,CAC/B,MAAM,EAAE,wBAAwB,EAChC,SAAS,EAAE,IAAI,CAAC,aAAa,CAAC,iBAAiB,EAAE,eAAe,CAAC,GAChE,OAAO,CAAC,aAAa,CAAC,CAGxB;AAGD;;;;;;;;;;;GAWG;AACH,wBAAgB,YAAY,CAC1B,MAAM,EAAE,yBAAyB,EACjC,SAAS,EAAE,IAAI,CAAC,aAAa,CAAC,iBAAiB,EAAE,eAAe,CAAC,GAChE,OAAO,CAAC,aAAa,CAAC,CAGxB;AAGD;;;;;;GAMG;AACH,wBAAgB,WAAW,CACzB,MAAM,EAAE,wBAAwB,EAChC,SAAS,EAAE,IAAI,CAAC,aAAa,CAAC,iBAAiB,EAAE,oBAAoB,CAAC,GACrE,OAAO,CAAC,OAAO,CAAC,CAGlB;AAED;;;;;;;GAOG;AACH,wBAAgB,kBAAkB,CAChC,MAAM,EAAE,IAAI,CAAC,aAAa,EAAE,aAAa,CAAC,EAC1C,SAAS,EAAE,IAAI,CAAC,aAAa,CAAC,iBAAiB,EAAE,kBAAkB,CAAC,GACnE,OAAO,CAAC,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC,CAY9B"}