import { Redirect, Stack } from 'expo-router'
import { Platform } from 'react-native'

// Conditional import for useAuth
let useAuth;
if (Platform.OS === 'web') {
  // Mock auth for web
  useAuth = () => ({ isSignedIn: true });
} else {
  try {
    const clerk = require('@clerk/clerk-expo');
    useAuth = clerk.useAuth;
  } catch (error) {
    // Fallback to mock
    useAuth = () => ({ isSignedIn: true });
  }
}

export default function AuthRoutesLayout() {
  const { isSignedIn } = useAuth()

  if (isSignedIn) {
    return <Redirect href={'/'} />
  }

  return <Stack screenOptions={{ headerShown: false }} />
}