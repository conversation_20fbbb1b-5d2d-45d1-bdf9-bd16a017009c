import { sql } from './config/db.js';

async function addAllSampleData() {
    try {
        console.log('Adding sample data to all tables...');

        // 1. Categories
        const categories = [
            { name: 'Electronics' },
            { name: 'Fashion' },
            { name: 'Home & Garden' },
            { name: 'Sports' },
            { name: 'Books' },
            { name: 'Automotive' }
        ];

        for (const category of categories) {
            await sql`
                INSERT INTO categories (name)
                VALUES (${category.name})
            `;
        }

        // 2. Users
        const users = [
            {
                name: '<PERSON>',
                email: '<EMAIL>',
                username: 'johndo<PERSON>',
                password: 'hashed_password_123',
                role: 'seller',
                status: 'active',
                approval_status: 'approved',
                is_email_verified: true,
                can_bid: true,
                can_participate_in_lottery: true,
                profile: { bio: 'Tech enthusiast', avatar: 'avatar1.jpg' }
            },
            {
                name: '<PERSON>',
                email: '<EMAIL>',
                username: 'jane<PERSON>',
                password: 'hashed_password_456',
                role: 'buyer',
                status: 'active',
                approval_status: 'approved',
                is_email_verified: true,
                can_bid: true,
                can_participate_in_lottery: true,
                profile: { bio: 'Fashion lover', avatar: 'avatar2.jpg' }
            },
            {
                name: '<PERSON>',
                email: '<EMAIL>',
                username: 'mikejohnson',
                password: 'hashed_password_789',
                role: 'seller',
                status: 'active',
                approval_status: 'approved',
                is_email_verified: true,
                can_bid: true,
                can_participate_in_lottery: true,
                profile: { bio: 'Sports equipment specialist', avatar: 'avatar3.jpg' }
            }
        ];

        for (const user of users) {
            await sql`
                INSERT INTO users (
                    name, email, username, password, role, status, 
                    approval_status, is_email_verified, can_bid, 
                    can_participate_in_lottery, profile
                ) VALUES (
                    ${user.name}, ${user.email}, ${user.username}, 
                    ${user.password}, ${user.role}, ${user.status},
                    ${user.approval_status}, ${user.is_email_verified}, 
                    ${user.can_bid}, ${user.can_participate_in_lottery},
                    ${JSON.stringify(user.profile)}
                )

            `;
        }

        // 3. Addresses
        const addresses = [
            {
                user_id: 'user_2example123',
                address: {
                    street: '123 Main St',
                    city: 'New York',
                    state: 'NY',
                    zip: '10001',
                    country: 'USA'
                },
                is_default: true
            },
            {
                user_id: 'user_2example123',
                address: {
                    street: '456 Oak Ave',
                    city: 'Los Angeles',
                    state: 'CA',
                    zip: '90210',
                    country: 'USA'
                },
                is_default: false
            }
        ];

        for (const addr of addresses) {
            await sql`
                INSERT INTO addresses (user_id, address, is_default)
                VALUES (${addr.user_id}, ${JSON.stringify(addr.address)}, ${addr.is_default})
            `;
        }

        // 4. More Products
        const moreProducts = [
            {
                name: 'Dell XPS 13 Laptop',
                description: 'Ultra-portable laptop with Intel i7 processor',
                price: 1199.99,
                original_price: 1299.99,
                discount_percentage: 7.69,
                images: ['https://example.com/dell-xps13.jpg'],
                category: 'Electronics',
                stock: 15,
                featured: true,
                status: 'active',
                seller_id: 'user_2example123',
                specifications: { processor: 'Intel i7', ram: '16GB', storage: '512GB SSD' },
                tags: ['laptop', 'dell', 'ultrabook'],
                dynamic_pricing: {}
            },
            {
                name: 'Adidas Running Shoes',
                description: 'Comfortable running shoes for daily training',
                price: 89.99,
                original_price: 109.99,
                discount_percentage: 18.18,
                images: ['https://example.com/adidas-running.jpg'],
                category: 'Sports',
                stock: 50,
                featured: false,
                status: 'active',
                seller_id: 'user_2example123',
                specifications: { size: '42', color: 'Blue/White', material: 'Mesh' },
                tags: ['shoes', 'adidas', 'running', 'sports'],
                dynamic_pricing: {}
            },
            {
                name: 'Wireless Bluetooth Speaker',
                description: 'Portable speaker with excellent sound quality',
                price: 49.99,
                original_price: 69.99,
                discount_percentage: 28.57,
                images: ['https://example.com/bluetooth-speaker.jpg'],
                category: 'Electronics',
                stock: 80,
                featured: true,
                status: 'active',
                seller_id: 'user_2example123',
                specifications: { battery: '12 hours', connectivity: 'Bluetooth 5.0' },
                tags: ['speaker', 'bluetooth', 'portable'],
                dynamic_pricing: {}
            }
        ];

        for (const product of moreProducts) {
            await sql`
                INSERT INTO products (
                    name, description, price, original_price, discount_percentage,
                    images, category, stock, featured, status, seller_id,
                    specifications, tags, dynamic_pricing
                ) VALUES (
                    ${product.name}, ${product.description}, ${product.price}, 
                    ${product.original_price}, ${product.discount_percentage},
                    ${JSON.stringify(product.images)}, ${product.category}, 
                    ${product.stock}, ${product.featured}, ${product.status}, 
                    ${product.seller_id}, ${JSON.stringify(product.specifications)}, 
                    ${JSON.stringify(product.tags)}, ${JSON.stringify(product.dynamic_pricing)}
                )
            `;
        }

        // 5. Coupons
        const coupons = [
            {
                code: 'SAVE10',
                discount_percentage: 10,
                min_amount: 50,
                max_amount: 1000,
                start_date: '2025-01-01',
                end_date: '2025-12-31',
                is_active: true
            },
            {
                code: 'WELCOME20',
                discount_percentage: 20,
                min_amount: 100,
                max_amount: 500,
                start_date: '2025-01-01',
                end_date: '2025-06-30',
                is_active: true
            }
        ];

        for (const coupon of coupons) {
            await sql`
                INSERT INTO coupons (
                    code, discount_percentage, min_amount, max_amount,
                    start_date, end_date, is_active
                ) VALUES (
                    ${coupon.code}, ${coupon.discount_percentage}, ${coupon.min_amount},
                    ${coupon.max_amount}, ${coupon.start_date}, ${coupon.end_date}, ${coupon.is_active}
                )

            `;
        }

        console.log('✅ All sample data added successfully!');
        console.log('📊 Added:');
        console.log('- 6 Categories');
        console.log('- 3 Users');
        console.log('- 2 Addresses');
        console.log('- 3 Additional Products');
        console.log('- 2 Coupons');
        
        process.exit(0);
    } catch (error) {
        console.error('❌ Error adding sample data:', error);
        process.exit(1);
    }
}

addAllSampleData();
