{"version": 3, "file": "Headings.js", "sourceRoot": "", "sources": ["../../src/elements/Headings.tsx"], "names": [], "mappings": "AAAA,OAAO,KAAK,EAAE,EAAiB,UAAU,EAAE,MAAM,OAAO,CAAC;AACzD,OAAO,EAAE,QAAQ,EAAE,UAAU,EAAE,MAAM,cAAc,CAAC;AAEpD,OAAO,EAAE,EAAE,EAAE,MAAM,cAAc,CAAC;AAClC,OAAO,IAAmB,MAAM,oBAAoB,CAAC;AAErD,SAAS,sBAAsB,CAAC,KAAa;IAC3C,MAAM,WAAW,GAAQ,QAAQ,CAAC,MAAM,CAAC;QACvC,GAAG,EAAE;YACH,kBAAkB,EAAE,KAAK;SAC1B;QACD,OAAO,EAAE,EAAE;KACZ,CAAC,CAAC;IACH,OAAO,UAAU,CAAC,CAAC,KAAgB,EAAE,GAAG,EAAE,EAAE;QAC1C,OAAO,CACL,oBAAC,IAAI,OACC,WAAW,EACf,iBAAiB,EAAC,QAAQ,KACtB,KAAK,EACT,KAAK,EAAE,CAAC,MAAM,CAAC,IAAI,KAAK,EAAE,CAAC,EAAE,KAAK,CAAC,KAAK,CAAC,EACzC,GAAG,EAAE,GAAG,GACR,CACH,CAAC;IACJ,CAAC,CAA6B,CAAC;AACjC,CAAC;AAED,MAAM,CAAC,MAAM,EAAE,GAAG,sBAAsB,CAAC,CAAC,CAAC,CAAC;AAC5C,MAAM,CAAC,MAAM,EAAE,GAAG,sBAAsB,CAAC,CAAC,CAAC,CAAC;AAC5C,MAAM,CAAC,MAAM,EAAE,GAAG,sBAAsB,CAAC,CAAC,CAAC,CAAC;AAC5C,MAAM,CAAC,MAAM,EAAE,GAAG,sBAAsB,CAAC,CAAC,CAAC,CAAC;AAC5C,MAAM,CAAC,MAAM,EAAE,GAAG,sBAAsB,CAAC,CAAC,CAAC,CAAC;AAC5C,MAAM,CAAC,MAAM,EAAE,GAAG,sBAAsB,CAAC,CAAC,CAAC,CAAC;AAE5C,uFAAuF;AACvF,MAAM,MAAM,GAAG,UAAU,CAAC,MAAM,CAAC;IAC/B,EAAE,EAAE;QACF,QAAQ,EAAE,EAAE,CAAC,CAAC,CAAC;QACf,cAAc,EAAE,EAAE,CAAC,IAAI,CAAC;QACxB,UAAU,EAAE,MAAM;KACnB;IACD,EAAE,EAAE;QACF,QAAQ,EAAE,EAAE,CAAC,GAAG,CAAC;QACjB,cAAc,EAAE,EAAE,CAAC,IAAI,CAAC;QACxB,UAAU,EAAE,MAAM;KACnB;IACD,EAAE,EAAE;QACF,QAAQ,EAAE,EAAE,CAAC,IAAI,CAAC;QAClB,cAAc,EAAE,EAAE,CAAC,CAAC,CAAC;QACrB,UAAU,EAAE,MAAM;KACnB;IACD,EAAE,EAAE;QACF,QAAQ,EAAE,EAAE,CAAC,CAAC,CAAC;QACf,cAAc,EAAE,EAAE,CAAC,IAAI,CAAC;QACxB,UAAU,EAAE,MAAM;KACnB;IACD,EAAE,EAAE;QACF,QAAQ,EAAE,EAAE,CAAC,IAAI,CAAC;QAClB,cAAc,EAAE,EAAE,CAAC,IAAI,CAAC;QACxB,UAAU,EAAE,MAAM;KACnB;IACD,EAAE,EAAE;QACF,QAAQ,EAAE,EAAE,CAAC,IAAI,CAAC;QAClB,cAAc,EAAE,EAAE,CAAC,IAAI,CAAC;QACxB,UAAU,EAAE,MAAM;KACnB;CACF,CAAC,CAAC", "sourcesContent": ["import React, { ComponentType, forwardRef } from 'react';\nimport { Platform, StyleSheet } from 'react-native';\n\nimport { em } from '../css/units';\nimport Text, { TextProps } from '../primitives/Text';\n\nfunction createHeadingComponent(level: number): ComponentType<TextProps> {\n  const nativeProps: any = Platform.select({\n    web: {\n      accessibilityLevel: level,\n    },\n    default: {},\n  });\n  return forwardRef((props: TextProps, ref) => {\n    return (\n      <Text\n        {...nativeProps}\n        accessibilityRole=\"header\"\n        {...props}\n        style={[styles[`h${level}`], props.style]}\n        ref={ref}\n      />\n    );\n  }) as ComponentType<TextProps>;\n}\n\nexport const H1 = createHeadingComponent(1);\nexport const H2 = createHeadingComponent(2);\nexport const H3 = createHeadingComponent(3);\nexport const H4 = createHeadingComponent(4);\nexport const H5 = createHeadingComponent(5);\nexport const H6 = createHeadingComponent(6);\n\n// Default web styles: http://trac.webkit.org/browser/trunk/Source/WebCore/css/html.css\nconst styles = StyleSheet.create({\n  h1: {\n    fontSize: em(2),\n    marginVertical: em(0.67),\n    fontWeight: 'bold',\n  },\n  h2: {\n    fontSize: em(1.5),\n    marginVertical: em(0.83),\n    fontWeight: 'bold',\n  },\n  h3: {\n    fontSize: em(1.17),\n    marginVertical: em(1),\n    fontWeight: 'bold',\n  },\n  h4: {\n    fontSize: em(1),\n    marginVertical: em(1.33),\n    fontWeight: 'bold',\n  },\n  h5: {\n    fontSize: em(0.83),\n    marginVertical: em(1.67),\n    fontWeight: 'bold',\n  },\n  h6: {\n    fontSize: em(0.67),\n    marginVertical: em(2.33),\n    fontWeight: 'bold',\n  },\n});\n"]}