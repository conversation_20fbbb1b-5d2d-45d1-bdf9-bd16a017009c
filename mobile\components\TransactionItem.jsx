import React from 'react'
import { View, Text, TouchableOpacity } from 'react-native'
import {styles} from '../assets/styles/home.styles'
import { COLORS } from '../constants/colors'
import { Ionicons } from '@expo/vector-icons'
import { useTransactions } from '../hooks/useTransactions'
import { formatDate } from '../lib/utils'

const CATEGORY_ICONS = {
    "Food & Drinks": "fast-food",
    Shopping: "cart",
  Income: 'cash',
  Expense: 'cash',
  Transfer: 'swap-horizontal',
  Entertainment: 'game-controller',
  Transportation: 'car',
  Health: 'medkit',
  Education: 'book',
  Housing: 'home',
  Utilities: 'flash',
  Insurance: 'shield',
  Debt: 'warning',
  Savings: 'save',
  Gifts: 'gift',
  Taxes: 'warning',
  Other: 'ellipsis-horizontal',

};




export const TransactionItem = ({ transaction, deleteTransaction, item, onDelete }) => {
  // Handle both prop patterns for backward compatibility
  const transactionData = transaction || item;
  const deleteHandler = deleteTransaction || onDelete;

  if (!transactionData) return null;

  const isIncome = parseFloat(transactionData.amount) > 0;
  const iconname = CATEGORY_ICONS[transactionData.category] || "pricetag-outline";

  return (
    <View style={styles.transactionCard} key={transactionData.id}>
      <TouchableOpacity style={styles.transactionContent}>
        <View style={styles.categoryIconContainer}>
          <Ionicons name={iconname} size={24} color={isIncome ? COLORS.income : COLORS.expense } />
        </View>
        <View style={styles.transactionLeft}>
          <Text style={styles.transactionTitle}>{transactionData.title}</Text>
          <Text style={styles.transactionCategory}>{transactionData.category}</Text>
        </View>
        <View style={styles.transactionRight}>
          <Text style={[styles.transactionAmount, {color: isIncome ? COLORS.income : COLORS.expense}]}>
            {isIncome ? '+' : '-'}${Math.abs(parseFloat(transactionData.amount)).toFixed(2)}
          </Text>
          <Text style={styles.transactionDate}>{formatDate(transactionData.created_at)}</Text>
        </View>
      </TouchableOpacity>
      <TouchableOpacity style={styles.deleteButton} onPress={() => deleteHandler(transactionData.id)}>
        <Ionicons name="trash-outline" size={24} color={COLORS.text} />
      </TouchableOpacity>
    </View>
  )
}


  