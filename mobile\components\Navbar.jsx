import React from 'react';
import { View, Text, TouchableOpacity, TextInput, Image } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { COLORS } from '../constants/colors';
import { SignOutButton } from './SignOutButton';
import { styles } from '../assets/styles/navbar.styles';

export const Navbar = ({ user, searchQuery, setSearchQuery, router }) => {
  const handleNavigation = (route) => {
    router.push(route);
  };

  const handleUserProfile = () => {
    router.push('/dashboard');
  };

  return (
    <View style={styles.navbar}>
      {/* Sol Taraf - Logo ve Marka */}
      <View style={styles.leftSection}>
        <Image
          source={require('../assets/images/revenue-2.png')}
          style={styles.logo}
          resizeMode="contain"
        />
        <Text style={styles.brandName}>Robinhood</Text>
      </View>

      {/* Orta Taraf - Navigasyon Linkleri */}
      <View style={styles.centerSection}>
        <TouchableOpacity 
          style={styles.navLink} 
          onPress={() => handleNavigation('/')}
        >
          <Text style={styles.navLinkText}>Ürünler</Text>
        </TouchableOpacity>
        
        <TouchableOpacity 
          style={styles.navLink} 
          onPress={() => handleNavigation('/auctions')}
        >
          <Text style={styles.navLinkText}>Açık Artırma</Text>
        </TouchableOpacity>
        
        <TouchableOpacity 
          style={styles.navLink} 
          onPress={() => handleNavigation('/lottery')}
        >
          <Text style={styles.navLinkText}>Çekiliş</Text>
        </TouchableOpacity>
        
        <TouchableOpacity 
          style={styles.navLink} 
          onPress={() => handleNavigation('/exchange')}
        >
          <Text style={styles.navLinkText}>Takas</Text>
        </TouchableOpacity>
      </View>

      {/* Sağ Taraf - Search, User, Logout */}
      <View style={styles.rightSection}>
        {/* Search */}
        <View style={styles.searchContainer}>
          <Ionicons name="search" size={20} color={COLORS.textLight} style={styles.searchIcon} />
          <TextInput
            style={styles.searchInput}
            placeholder="Ara..."
            value={searchQuery}
            onChangeText={setSearchQuery}
            placeholderTextColor={COLORS.textLight}
          />
        </View>

        {/* User Info */}
        <TouchableOpacity style={styles.userInfo} onPress={handleUserProfile}>
          <Ionicons name="person-circle" size={24} color={COLORS.primary} />
          <Text style={styles.userEmail}>
            {user?.emailAddresses[0]?.emailAddress?.split('@')[0] || 'User'}
          </Text>
        </TouchableOpacity>

        {/* Logout Button */}
        <SignOutButton />
      </View>
    </View>
  );
};
