import {neon} from "@neondatabase/serverless";
import "dotenv/config";

// create a connection using out database url
export const sql = neon(process.env.DATABASE_URL);

export async function initDB() {
    try {
        await sql`CREATE TABLE IF NOT EXISTS transactions(
            id SERIAL PRIMARY KEY,
            user_id VARCHAR(255) NOT NULL,
            title VARCHAR(255) NOT NULL,
            amount DECIMAL NOT NULL,
            category VARCHAR(255) NOT NULL,
            created_at DATE NOT NULL DEFAULT CURRENT_DATE
        )`;
        await sql`CREATE TABLE IF NOT EXISTS users(
            id SERIAL PRIMARY KEY,
            name VA<PERSON>HA<PERSON>(255) NOT NULL,
            email VARCHAR(255) NOT NULL,
            username VARCHAR(255) NOT NULL,
            password VARCHAR(255) NOT NULL,
            role VARCHAR(255) NOT NULL,
            status VARCHAR(255) NOT NULL,
            approval_status VARCHAR(255) NOT NULL,
            is_email_verified BOOLEAN NOT NULL,
            can_bid BOOLEAN NOT NULL,
            can_participate_in_lottery BOOLEAN NOT NULL,
            profile JSONB NOT NULL,
            created_at DATE NOT NULL DEFAULT CURRENT_DATE,
            updated_at DATE NOT NULL DEFAULT CURRENT_DATE
        )`;
        await sql`CREATE TABLE IF NOT EXISTS products(
            id SERIAL PRIMARY KEY,
            name VARCHAR(255) NOT NULL,
            description TEXT NOT NULL,
            price DECIMAL NOT NULL,
            original_price DECIMAL NOT NULL,
            discount_percentage DECIMAL NOT NULL,
            images JSONB NOT NULL,
            category VARCHAR(255) NOT NULL,
            stock INTEGER NOT NULL,
            featured BOOLEAN NOT NULL,
            status VARCHAR(255) NOT NULL,
            seller_id VARCHAR(255) NOT NULL,
            specifications JSONB NOT NULL,
            tags JSONB NOT NULL,
            dynamic_pricing JSONB NOT NULL,
            created_at DATE NOT NULL DEFAULT CURRENT_DATE,
            updated_at DATE NOT NULL DEFAULT CURRENT_DATE
        )`;
        await sql`CREATE TABLE IF NOT EXISTS orders(
            id SERIAL PRIMARY KEY,
            user_id VARCHAR(255) NOT NULL,
            items JSONB NOT NULL,
            total_amount DECIMAL NOT NULL,
            shipping_address JSONB NOT NULL,
            billing_address JSONB NOT NULL,
            payment_method VARCHAR(255) NOT NULL,
            payment_status VARCHAR(255) NOT NULL,
            status VARCHAR(255) NOT NULL,
            order_number VARCHAR(255) NOT NULL,
            notes TEXT NOT NULL,
            shipping_cost DECIMAL NOT NULL,
            tax_amount DECIMAL NOT NULL,
            discount_amount DECIMAL NOT NULL,
            coupon_code VARCHAR(255) NOT NULL,
            tracking_number VARCHAR(255) NOT NULL,
            estimated_delivery DATE NOT NULL,
            delivered_at DATE NOT NULL,
            cancelled_at DATE NOT NULL,
            cancellation_reason TEXT NOT NULL,
            created_at DATE NOT NULL DEFAULT CURRENT_DATE,
            updated_at DATE NOT NULL DEFAULT CURRENT_DATE
        )`;
        await sql`CREATE TABLE IF NOT EXISTS auctions(
            id SERIAL PRIMARY KEY,
            product_id VARCHAR(255) NOT NULL,
            seller_id VARCHAR(255) NOT NULL,
            title VARCHAR(255) NOT NULL,
            description TEXT NOT NULL,
            starting_price DECIMAL NOT NULL,
            current_price DECIMAL NOT NULL,
            buy_now_price DECIMAL NOT NULL,
            min_bid_increment DECIMAL NOT NULL,
            start_time TIMESTAMP NOT NULL,
            end_time TIMESTAMP NOT NULL,
            status VARCHAR(255) NOT NULL,
            bids JSONB NOT NULL,
            winner_id VARCHAR(255) NOT NULL,
            winning_bid JSONB NOT NULL,
            auto_extend BOOLEAN NOT NULL,
            extend_minutes INTEGER NOT NULL,
            view_count INTEGER NOT NULL,
            watcher_count INTEGER NOT NULL,
            created_at DATE NOT NULL DEFAULT CURRENT_DATE,
            updated_at DATE NOT NULL DEFAULT CURRENT_DATE
        )`;
        await sql`CREATE TABLE IF NOT EXISTS lotteries(
            id SERIAL PRIMARY KEY,
            title VARCHAR(255) NOT NULL,
            description TEXT NOT NULL,
            ticket_price DECIMAL NOT NULL,
            max_tickets INTEGER NOT NULL,
            numbers JSONB NOT NULL,
            tickets JSONB NOT NULL,
            start_time TIMESTAMP NOT NULL,
            end_time TIMESTAMP NOT NULL,
            status VARCHAR(255) NOT NULL,
            winner_id VARCHAR(255) NOT NULL,
            winning_number INTEGER NOT NULL,
            winner_username VARCHAR(255) NOT NULL,
            draw_time TIMESTAMP NOT NULL,
            total_prize DECIMAL NOT NULL,
            created_at DATE NOT NULL DEFAULT CURRENT_DATE,
            updated_at DATE NOT NULL DEFAULT CURRENT_DATE
        )`;
        await sql`CREATE TABLE IF NOT EXISTS bids(
            id SERIAL PRIMARY KEY,
            auction_id VARCHAR(255) NOT NULL,
            user_id VARCHAR(255) NOT NULL,
            amount DECIMAL NOT NULL,
            timestamp TIMESTAMP NOT NULL,
            created_at DATE NOT NULL DEFAULT CURRENT_DATE,
            updated_at DATE NOT NULL DEFAULT CURRENT_DATE
        )`;
        await sql`CREATE TABLE IF NOT EXISTS tickets(
            id SERIAL PRIMARY KEY,
            lottery_id VARCHAR(255) NOT NULL,
            user_id VARCHAR(255) NOT NULL,
            numbers JSONB NOT NULL,
            purchase_time TIMESTAMP NOT NULL,
            ticket_id VARCHAR(255) NOT NULL,
            created_at DATE NOT NULL DEFAULT CURRENT_DATE,
            updated_at DATE NOT NULL DEFAULT CURRENT_DATE
        )`;
        await sql`CREATE TABLE IF NOT EXISTS notifications(
            id SERIAL PRIMARY KEY,
            user_id VARCHAR(255) NOT NULL,
            message TEXT NOT NULL,
            is_read BOOLEAN NOT NULL,
            created_at DATE NOT NULL DEFAULT CURRENT_DATE,
            updated_at DATE NOT NULL DEFAULT CURRENT_DATE
        )`;
        await sql`CREATE TABLE IF NOT EXISTS reviews(
            id SERIAL PRIMARY KEY,
            user_id VARCHAR(255) NOT NULL,
            product_id VARCHAR(255) NOT NULL,
            rating INTEGER NOT NULL,
            comment TEXT NOT NULL,
            created_at DATE NOT NULL DEFAULT CURRENT_DATE,
            updated_at DATE NOT NULL DEFAULT CURRENT_DATE
        )`;
        await sql`CREATE TABLE IF NOT EXISTS wishlists(
            id SERIAL PRIMARY KEY,
            user_id VARCHAR(255) NOT NULL,
            product_id VARCHAR(255) NOT NULL,
            created_at DATE NOT NULL DEFAULT CURRENT_DATE,
            updated_at DATE NOT NULL DEFAULT CURRENT_DATE
        )`;
        await sql`CREATE TABLE IF NOT EXISTS carts(
            id SERIAL PRIMARY KEY,
            user_id VARCHAR(255) NOT NULL,
            items JSONB NOT NULL,
            created_at DATE NOT NULL DEFAULT CURRENT_DATE,
            updated_at DATE NOT NULL DEFAULT CURRENT_DATE
        )`;
        await sql`CREATE TABLE IF NOT EXISTS addresses(
            id SERIAL PRIMARY KEY,
            user_id VARCHAR(255) NOT NULL,
            address JSONB NOT NULL,
            is_default BOOLEAN NOT NULL,
            created_at DATE NOT NULL DEFAULT CURRENT_DATE,
            updated_at DATE NOT NULL DEFAULT CURRENT_DATE
        )`;
        await sql`CREATE TABLE IF NOT EXISTS payments(
            id SERIAL PRIMARY KEY,
            user_id VARCHAR(255) NOT NULL,
            payment_method VARCHAR(255) NOT NULL,
            card_number VARCHAR(255) NOT NULL,
            expiry_date VARCHAR(255) NOT NULL,
            cvv VARCHAR(255) NOT NULL,
            is_default BOOLEAN NOT NULL,
            created_at DATE NOT NULL DEFAULT CURRENT_DATE,
            updated_at DATE NOT NULL DEFAULT CURRENT_DATE
        )`;
        await sql`CREATE TABLE IF NOT EXISTS coupons(
            id SERIAL PRIMARY KEY,
            code VARCHAR(255) NOT NULL,
            discount_percentage DECIMAL NOT NULL,
            min_amount DECIMAL NOT NULL,
            max_amount DECIMAL NOT NULL,
            start_date DATE NOT NULL,
            end_date DATE NOT NULL,
            is_active BOOLEAN NOT NULL,
            created_at DATE NOT NULL DEFAULT CURRENT_DATE,
            updated_at DATE NOT NULL DEFAULT CURRENT_DATE
        )`;
        await sql`CREATE TABLE IF NOT EXISTS settings(
            id SERIAL PRIMARY KEY,
            key VARCHAR(255) NOT NULL,
            value VARCHAR(255) NOT NULL,
            created_at DATE NOT NULL DEFAULT CURRENT_DATE,
            updated_at DATE NOT NULL DEFAULT CURRENT_DATE
        )`;
        await sql`CREATE TABLE IF NOT EXISTS categories(
            id SERIAL PRIMARY KEY,
            name VARCHAR(255) NOT NULL,
            created_at DATE NOT NULL DEFAULT CURRENT_DATE,
            updated_at DATE NOT NULL DEFAULT CURRENT_DATE
        )`;
        await sql`CREATE TABLE IF NOT EXISTS subcategories(
            id SERIAL PRIMARY KEY,
            category_id VARCHAR(255) NOT NULL,
            name VARCHAR(255) NOT NULL,
            created_at DATE NOT NULL DEFAULT CURRENT_DATE,
            updated_at DATE NOT NULL DEFAULT CURRENT_DATE
        )`;
        await sql`CREATE TABLE IF NOT EXISTS product_categories(
            id SERIAL PRIMARY KEY,
            product_id VARCHAR(255) NOT NULL,
            category_id VARCHAR(255) NOT NULL,
            created_at DATE NOT NULL DEFAULT CURRENT_DATE,
            updated_at DATE NOT NULL DEFAULT CURRENT_DATE
        )`;
        await sql`CREATE TABLE IF NOT EXISTS product_subcategories(
            id SERIAL PRIMARY KEY,
            product_id VARCHAR(255) NOT NULL,
            subcategory_id VARCHAR(255) NOT NULL,
            created_at DATE NOT NULL DEFAULT CURRENT_DATE,
            updated_at DATE NOT NULL DEFAULT CURRENT_DATE
        )`;
        console.log('Database initialized successfully');
    } catch (error) {
        console.log('Error initializing DB', error);
        process.exit(1);//status code 1 means falure 0 means success 
    }
}

