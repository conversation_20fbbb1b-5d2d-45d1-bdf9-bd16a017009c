{"version": 3, "file": "createSafeStyledView.native.js", "sourceRoot": "", "sources": ["../../src/css/createSafeStyledView.native.tsx"], "names": [], "mappings": "AAAA,OAAO,KAAK,EAAE,EAAE,OAAO,EAAE,MAAM,OAAO,CAAC;AAEvC,OAAO,EAAE,YAAY,EAAE,MAAM,gBAAgB,CAAC;AAE9C,MAAM,UAAU,oBAAoB,CAAyC,IAAW;IACtF,OAAO,KAAK,CAAC,UAAU,CAAC,CAAC,EAAE,KAAK,EAAE,GAAG,KAAK,EAAO,EAAE,YAA8B,EAAE,EAAE;QACnF,kCAAkC;QAClC,MAAM,UAAU,GAAG,OAAO,CAAC,GAAG,EAAE,CAAC,YAAY,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC;QAE/D,OAAO,oBAAC,IAAI,IAAC,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,UAAU,KAAM,KAAK,GAAI,CAAC;IACnE,CAAC,CAAC,CAAC;AACL,CAAC", "sourcesContent": ["import React, { useMemo } from 'react';\n\nimport { filterStyles } from './filterStyles';\n\nexport function createSafeStyledView<TView extends React.ComponentType<any>>(View: TView) {\n  return React.forwardRef(({ style, ...props }: any, forwardedRef: React.Ref<TView>) => {\n    // Filter and apply `center` prop.\n    const finalStyle = useMemo(() => filterStyles(style), [style]);\n\n    return <View ref={forwardedRef} style={finalStyle} {...props} />;\n  });\n}\n"]}