import { View, Text, Alert, ActivityIndicator, TextInput, TouchableOpacity} from 'react-native'
import {styles} from '../../assets/styles/create.styles'
import { useRouter } from 'expo-router'
import { useState } from 'react'
import { API_URL } from '../../constants/api'
import { useUser } from '@clerk/clerk-expo'
import { Ionicons } from '@expo/vector-icons'
import { COLORS } from '../../constants/colors'



const CATEGORIES = 
[
  { id: 1, name:"Food & Drinks", icon:"fast-food"},
  { id: 2, name:"Shopping", icon:"cart"},
  { id: 3, name: "Income", icon: "cash" },
  { id: 4, name: "Expense", icon: "cash" },
  { id: 5, name: "Transfer", icon: "swap-horizontal" },
  { id: 6, name: "Entertainment", icon: "game-controller" },
  { id: 7, name: "Transportation", icon: "car" },
  { id: 8, name: "Health", icon: "medkit" },
  { id: 9, name: "Education", icon: "book" },
  { id: 10, name: "Housing", icon: "home" },
  ];

const CreateScreen = () => {
    const router = useRouter();
    const {user} = useUser();
    const [selectedCategory, setSelectedCategory] = useState("");
    const [amount, setAmount] = useState('');
    const [title, setTitle] = useState('');
    const [isExpense, setExpense] = useState(true);
    const [isLoading, setIsLoading] = useState(false);


    const handleCreate = async () => {
        
        
        if (!title.trim() ) return Alert.alert("Error", "Please enter a title");
        if (!amount || 	isNaN(parseFloat(amount)) || parseFloat(amount <= 0) ) return Alert.alert("Error", "Please enter an amount greater than 0");
        if (!selectedCategory.trim() ) return Alert.alert("Error", "Please select a category");

        setIsLoading(true);
        try {
            const formattedAmount = isExpense
            ? -Math.abs(parseFloat(amount)) :
             Math.abs(parseFloat(amount));


            const response = await fetch(`${API_URL}/transactions`, {
                method: 'POST',
                headers: {
                  'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                  user_id: user.id,
                  title,
                  amount: formattedAmount,
                  category: selectedCategory,
                }),
              });
              if (!response.ok) {
                throw new Error('Failed to create transaction');
              }
              await response.json();
              router.back();
          } catch (error) {
            console.error('Error creating transaction', error);
          } finally {
            setIsLoading(false);
          }
        }

        const handleCategoryPress = (category) => {
          setSelectedCategory(category);
        };


  return (
    <View style={styles.container}>
        <View style={styles.header}>
            <TouchableOpacity style={styles.backButton} onPress={() => router.back()}>
                <Ionicons name="arrow-back" size={24} color={COLORS.text} />
            </TouchableOpacity>
            <Text style={styles.headerTitle}>New Transaction</Text>
            <TouchableOpacity
            style={[styles.saveButtonContainer, !title.trim() || !amount.trim() || !selectedCategory.trim() ? styles.saveButtonDisabled : null]}
            onPress={handleCreate}
            disabled={!title.trim() || !amount.trim() || !selectedCategory.trim()}
            >
                <Ionicons name="checkmark" size={24} color={COLORS.white} />
                <Text style={styles.saveButton}>Save</Text>
            </TouchableOpacity>
        </View>
        <View style={styles.card}>
            <View style={styles.typeSelector}>
                <TouchableOpacity
                style={[styles.typeButton, !isExpense ? styles.typeButtonActive : null]}
                onPress={() => setExpense(false)}
                >
                    <Ionicons name="add" size={24} color={isExpense ? COLORS.text : COLORS.white} />
                    <Text style={[styles.typeButtonText, !isExpense ? styles.typeButtonTextActive : null]}>Income</Text>
                </TouchableOpacity>
                <TouchableOpacity
                style={[styles.typeButton, isExpense ? styles.typeButtonActive : null]}
                onPress={() => setExpense(true)}
                >
                    <Ionicons name="remove" size={24} color={isExpense ? COLORS.white : COLORS.text} />
                    <Text style={[styles.typeButtonText, isExpense ? styles.typeButtonTextActive : null]}>Expense</Text>
                </TouchableOpacity>
            </View>
            <View style={styles.amountContainer}>
                <Text style={styles.currencySymbol}>$</Text>
                <TextInput
                style={styles.amountInput}
                value={amount}
                placeholder="0.00"
                placeholderTextColor={COLORS.textLight}
                keyboardType="numeric"
                onChangeText={(amount) => setAmount(amount)}
                />
            </View>
            <View style={styles.inputContainer}>
                <Ionicons name="pricetags" size={24} color={COLORS.text} style={styles.inputIcon} />
                <TextInput
                style={styles.input}
                value={title}
                placeholder="Title"
                placeholderTextColor={COLORS.textLight}
                onChangeText={(title) => setTitle(title)}
                />
            </View>
            <Text style={styles.sectionTitle}>Category</Text>
            <View style={styles.categoryGrid}>
                {CATEGORIES.map((category) => (                 
                <TouchableOpacity
                    key={category.id}
                    style={[styles.categoryButton, selectedCategory === category.name ? styles.categoryButtonActive : null]}
                    onPress={() => handleCategoryPress(category.name)}
                >
                    <Ionicons name={category.icon} size={24} color={selectedCategory === category.name ? COLORS.white : COLORS.text} />
                    <Text style={[styles.categoryButtonText, selectedCategory === category.name ? styles.categoryButtonTextActive : null]}>
                    {category.name}
                    </Text>
                </TouchableOpacity>
                ))}
            </View>
        </View>
        {isLoading && (
            <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={COLORS.primary}/>
            </View>
        )}
    </View>
  ) 
};

export default CreateScreen;