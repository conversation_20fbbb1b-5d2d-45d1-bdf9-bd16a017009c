import { useState, useEffect, useCallback } from 'react';
import { API_URL } from '../constants/api';



export const useProducts = () => {
  const [products, setProducts] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [featuredProducts, setFeaturedProducts] = useState([]);



  const fetchProducts = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);
      console.log('Fetching products from:', `${API_URL}/products`);
      const response = await fetch(`${API_URL}/products`);

      console.log('Response status:', response.status);
      if (!response.ok) {
        throw new Error(`Failed to fetch products: ${response.status}`);
      }

      const data = await response.json();
      console.log('Products data:', data);
      setProducts(data);
    } catch (err) {
      setError(err.message);
      console.error('Error fetching products:', err);
    } finally {
      setIsLoading(false);
    }
  }, []);

  const getImageUrl = (product) => {
    try {
      if (!product.images) {
        return 'https://via.placeholder.com/400x300/f0f0f0/999999?text=No+Image';
      }
      
      if (typeof product.images === 'string') {
        try {
          const parsedImages = JSON.parse(product.images);
          return Array.isArray(parsedImages) && parsedImages.length > 0 
            ? parsedImages[0] 
            : 'https://via.placeholder.com/400x300/f0f0f0/999999?text=No+Image';
        } catch (parseError) {
          return product.images.startsWith('http') 
            ? product.images 
            : 'https://via.placeholder.com/400x300/f0f0f0/999999?text=No+Image';
        }
      }
      
      if (Array.isArray(product.images) && product.images.length > 0) {
        return product.images[0];
      }
      return 'https://via.placeholder.com/400x300/f0f0f0/999999?text=No+Image';
    } catch (error) {
      console.warn('Error getting product image:', error);
      return 'https://via.placeholder.com/400x300/f0f0f0/999999?text=No+Image';
    }
  };
  const discountPercentage = (product) => {
    return product.original_price > product.price
      ? Math.round(((product.original_price - product.price) / product.original_price) * 100)
      : 0;
  };

  const getProducts = () => {
    return products.filter(product => product.status === 'active');
  };
  const getProductPrice = (product) => {
    return product.price;
  };


  const getFeaturedProducts = () => {
    return products.filter(product => product.featured && product.status === 'active');
  };
  const getProductsBySubcategory = (subcategory) => {
    return products.filter(product => product.subcategory === subcategory && product.status === 'active');
  };  
  
  const getProductsByCategory = (category) => {
    return products.filter(product => product.category === category && product.status === 'active');
  };

  useEffect(() => {
    fetchProducts();
  }, [fetchProducts]);

  return {
    products,
    isLoading,
    error,
    fetchProducts,
    getFeaturedProducts,
    getProductsByCategory,
    getProductsBySubcategory,
    getImageUrl,
    getProducts,
    getProductPrice,
    discountPercentage,

  };
};