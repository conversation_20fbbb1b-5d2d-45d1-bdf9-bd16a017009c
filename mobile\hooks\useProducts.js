import { useState, useEffect, useCallback } from 'react';
import { API_URL } from '../constants/api';

export const useProducts = () => {
  const [products, setProducts] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);

  const fetchProducts = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);
      const response = await fetch(`${API_URL}/api/products`);

      if (!response.ok) {
        throw new Error('Failed to fetch products');
      }

      const data = await response.json();
      setProducts(data);
    } catch (err) {
      setError(err.message);
      console.error('Error fetching products:', err);
    } finally {
      setIsLoading(false);
    }
  }, []);

  const getFeaturedProducts = () => {
    return products.filter(product => product.featured && product.status === 'active');
  };

  const getProductsByCategory = (category) => {
    return products.filter(product => product.category === category && product.status === 'active');
  };

  useEffect(() => {
    fetchProducts();
  }, [fetchProducts]);

  return {
    products,
    isLoading,
    error,
    fetchProducts,
    getFeaturedProducts,
    getProductsByCategory,
  };
};