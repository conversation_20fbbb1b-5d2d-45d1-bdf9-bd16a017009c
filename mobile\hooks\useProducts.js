
import { useState, useEffect, useCallback } from 'react';
import { API_URL } from '../constants/api';

export const useProducts = () => {
  const [products, setProducts] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [featuredProducts, setFeaturedProducts] = useState([]);
  const [userProducts, setUserProducts] = useState([]);
  const [searchResults, setSearchResults] = useState([]);
  const [categoryProducts, setCategoryProducts] = useState([]);
  const [product, setProduct] = useState(null);
  const [user, setUser] = useState(null);
  const [category, setCategory] = useState(null);
  const [searchQuery, setSearchQuery] = useState('');




  const fetchFeaturedProducts = useCallback(async () => {
    try {
      const response = await fetch(`${API_URL}/products/featured`);
      const data = await response.json();
      setFeaturedProducts(data);
    } catch (error) {
      console.error('Error fetching featured products', error);
    } finally {
      setIsLoading(false);
    }
  }, []);


  useEffect(() => {
    fetchFeaturedProducts();
  }, [fetchFeaturedProducts]);

    useEffect(() => {
    if (user) {
      fetchUserProducts();
    }
  }, [user, fetchUserProducts]);

    useEffect(() => {
    if (category) {
      fetchCategoryProducts();
    }
  }, [category, fetchCategoryProducts]);

    useEffect(() => {
    if (category) {
      fetchCategory();
    }
  }, [category, fetchCategory]);

    useEffect(() => {
    if (searchQuery) {
      fetchSearchResults();
    }
  }, [searchQuery, fetchSearchResults]);

    useEffect(() => {
    if (product) {
      fetchProductDetails();
    }
  }, [product, fetchProductDetails]);

    useEffect(() => {
    if (user) {
      fetchUser();
    }
  }, [user, fetchUser]);

  const fetchUserProducts = useCallback(async () => {
    try {
      const response = await fetch(`${API_URL}/products/user/${user.id}`);
      const data = await response.json();
      setUserProducts(data);
    } catch (error) {
      console.error('Error fetching user products', error);
    } finally {
      setIsLoading(false);
    }
  }, [user]);


  const fetchCategoryProducts = useCallback(async () => {
    try {
      const response = await fetch(`${API_URL}/products/category/${category}`);
      const data = await response.json();
      setCategoryProducts(data);
    } catch (error) {
      console.error('Error fetching category products', error);
    } finally {
      setIsLoading(false);
    }
  }, [category]);



  const fetchSearchResults = useCallback(async () => {
    try {
      const response = await fetch(`${API_URL}/products/search/${searchQuery}`);
      const data = await response.json();
      setSearchResults(data);
    } catch (error) {
      console.error('Error fetching search results', error);
    } finally {
      setIsLoading(false);
    }
  }, [searchQuery]);


  const fetchProductDetails = useCallback(async () => {
    try {
      const response = await fetch(`${API_URL}/products/${product.id}`);
      const data = await response.json();
      setProduct(data);
    } catch (error) {
      console.error('Error fetching product details', error);
    } finally {
      setIsLoading(false);
    }
  }, [product]);

  const fetchUser = useCallback(async () => {
    try {
      const response = await fetch(`${API_URL}/users/${user.id}`);
      const data = await response.json();
      setUser(data);
    } catch (error) {
      console.error('Error fetching user', error);
    } finally {
      setIsLoading(false);
    }
  }, [user]);


  const fetchCategory = useCallback(async () => {
    try {
      const response = await fetch(`${API_URL}/categories/${category}`);
      const data = await response.json();
      setCategory(data);
    } catch (error) {
      console.error('Error fetching category', error);
    } finally {
      setIsLoading(false);
    }
  }, [category]);

  const fetchProductsByCategory = useCallback(async () => {
    try {
      const response = await fetch(`${API_URL}/products/category/${category}`);
      const data = await response.json();
      setProducts(data);
    } catch (error) {
      console.error('Error fetching products by category', error);
    } finally {
      setIsLoading(false);
    }
  }, [category]);
  useEffect(() => {
    if (category) {
      fetchProductsByCategory();
    }
  }, [category, fetchProductsByCategory]);

  const fetchProductsByUser = useCallback(async () => {
    try {
      const response = await fetch(`${API_URL}/products/user/${user.id}`);
      const data = await response.json();
      setProducts(data);
    } catch (error) {
      console.error('Error fetching products by user', error);
    } finally {
      setIsLoading(false);
    }
  }, [user]);
  useEffect(() => {
    if (user) {
      fetchProductsByUser();
    }
  }, [user, fetchProductsByUser]);

  const fetchProductsBySearch = useCallback(async () => {
    try {
      const response = await fetch(`${API_URL}/products/search/${searchQuery}`);
      const data = await response.json();
      setProducts(data);
    } catch (error) {
      console.error('Error fetching products by search', error);
    } finally {
      setIsLoading(false);
    }
  }, [searchQuery]);
  useEffect(() => {
    if (searchQuery) {  
      fetchProductsBySearch();
    }
  }, [searchQuery, fetchProductsBySearch]);

  const fetchProductsByFeatured = useCallback(async () => {
    try {
      const response = await fetch(`${API_URL}/products/featured`);
      const data = await response.json();
      setProducts(data);
    } catch (error) {
      console.error('Error fetching products by featured', error);
    } finally {
      setIsLoading(false);
    }
  }, []);
  useEffect(() => {
    fetchProductsByFeatured();
  }, [fetchProductsByFeatured]);

  const fetchProducts = useCallback(async () => {
    try {
      const response = await fetch(`${API_URL}/products`);
      const data = await response.json();
      setProducts(data);
    } catch (error) {
      console.error('Error fetching products', error);
    } finally {
      setIsLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchProducts();
  }, [fetchProducts]);

  const loadData = useCallback(async () => {
    setIsLoading(true);
  
    await Promise.all([
      fetchProducts(),
      fetchFeaturedProducts(),
      fetchUserProducts(),
      fetchCategoryProducts(),
      fetchSearchResults(),
      fetchProductDetails(),
      fetchUser(),
      fetchCategory(),
    ]);
  
    setIsLoading(false);
  }, [
    fetchProducts,
    fetchFeaturedProducts,
    fetchUserProducts,
    fetchCategoryProducts,
    fetchSearchResults,
    fetchProductDetails,
    fetchUser,
    fetchCategory,
  ]);

  return { products, isLoading, fetchProducts };
};
