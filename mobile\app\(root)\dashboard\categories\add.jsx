import React, { useState } from 'react';
import { View, Text, TextInput, TouchableOpacity, ScrollView, Alert } from 'react-native';
import { useUser } from '@clerk/clerk-expo';
import { useRouter } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import { COLORS } from '@/constants/colors';
import { API_URL } from '@/constants/api';
import { Navbar } from '../../../../components/Navbar';
import { styles } from '../../../../assets/styles/form.styles';

export default function AddCategory() {
  const { user } = useUser();
  const router = useRouter();
  const [searchQuery, setSearchQuery] = useState('');
  const [loading, setLoading] = useState(false);
  const [categoryName, setCategoryName] = useState('');

  const handleSubmit = async () => {
    try {
      // Validation
      if (!categoryName.trim()) {
        Alert.alert('Hata', 'Lütfen kategori adını girin');
        return;
      }

      setLoading(true);

      const response = await fetch(`${API_URL}/api/categories`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ name: categoryName.trim() }),
      });

      if (response.ok) {
        Alert.alert('Başarılı', 'Kategori başarıyla eklendi', [
          { text: 'Tamam', onPress: () => router.back() }
        ]);
      } else {
        const errorData = await response.json();
        Alert.alert('Hata', errorData.message || 'Kategori eklenirken bir hata oluştu');
      }
    } catch (error) {
      console.error('Error adding category:', error);
      Alert.alert('Hata', 'Kategori eklenirken bir hata oluştu');
    } finally {
      setLoading(false);
    }
  };

  return (
    <View style={styles.container}>
      <Navbar 
        user={user} 
        searchQuery={searchQuery} 
        setSearchQuery={setSearchQuery}
        router={router}
      />
      
      <ScrollView style={styles.formContainer}>
        <View style={styles.header}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => router.back()}
          >
            <Ionicons name="arrow-back" size={24} color={COLORS.text} />
          </TouchableOpacity>
          <Text style={styles.title}>Yeni Kategori Ekle</Text>
        </View>

        <View style={styles.form}>
          <View style={styles.inputGroup}>
            <Text style={styles.label}>Kategori Adı *</Text>
            <TextInput
              style={styles.input}
              value={categoryName}
              onChangeText={setCategoryName}
              placeholder="Kategori adını girin"
              placeholderTextColor={COLORS.textLight}
            />
          </View>

          <TouchableOpacity
            style={[styles.submitButton, loading && styles.submitButtonDisabled]}
            onPress={handleSubmit}
            disabled={loading}
          >
            <Text style={styles.submitButtonText}>
              {loading ? 'Ekleniyor...' : 'Kategori Ekle'}
            </Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </View>
  );
}
