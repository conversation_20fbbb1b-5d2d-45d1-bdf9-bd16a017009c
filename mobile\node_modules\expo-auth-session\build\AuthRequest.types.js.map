{"version": 3, "file": "AuthRequest.types.js", "sourceRoot": "", "sources": ["../src/AuthRequest.types.ts"], "names": [], "mappings": "AAIA,cAAc;AACd,MAAM,CAAN,IAAY,mBAYX;AAZD,WAAY,mBAAmB;IAC7B;;;;;OAKG;IACH,oCAAa,CAAA;IACb;;OAEG;IACH,sCAAe,CAAA;AACjB,CAAC,EAZW,mBAAmB,KAAnB,mBAAmB,QAY9B;AAED,cAAc;AACd;;;;GAIG;AACH,MAAM,CAAN,IAAY,YAaX;AAbD,WAAY,YAAY;IACtB;;OAEG;IACH,6BAAa,CAAA;IACb;;OAEG;IACH,+BAAe,CAAA;IACf;;OAEG;IACH,oCAAoB,CAAA;AACtB,CAAC,EAbW,YAAY,KAAZ,YAAY,QAavB;AAED,cAAc;AACd;;;;;;GAMG;AACH,MAAM,CAAN,IAAY,MAsBX;AAtBD,WAAY,MAAM;IAChB;;;;OAIG;IACH,uBAAa,CAAA;IACb;;;OAGG;IACH,yBAAe,CAAA;IACf;;;OAGG;IACH,6BAAmB,CAAA;IACnB;;;OAGG;IACH,0CAAgC,CAAA;AAClC,CAAC,EAtBW,MAAM,KAAN,MAAM,QAsBjB", "sourcesContent": ["import { WebBrowserOpenOptions, WebBrowserWindowFeatures } from 'expo-web-browser';\n\nimport { DiscoveryDocument } from './Discovery';\n\n// @needsAudit\nexport enum CodeChallengeMethod {\n  /**\n   * The default and recommended method for transforming the code verifier.\n   * - Convert the code verifier to ASCII.\n   * - Create a digest of the string using crypto method SHA256.\n   * - Convert the digest to Base64 and URL encode it.\n   */\n  S256 = 'S256',\n  /**\n   * This should not be used. When used, the code verifier will be sent to the server as-is.\n   */\n  Plain = 'plain',\n}\n\n// @needsAudit\n/**\n * The client informs the authorization server of the desired grant type by using the response type.\n *\n * @see [Section 3.1.1](https://tools.ietf.org/html/rfc6749#section-3.1.1).\n */\nexport enum ResponseType {\n  /**\n   * For requesting an authorization code as described by [Section 4.1.1](https://tools.ietf.org/html/rfc6749#section-4.1.1).\n   */\n  Code = 'code',\n  /**\n   * For requesting an access token (implicit grant) as described by [Section 4.2.1](https://tools.ietf.org/html/rfc6749#section-4.2.1).\n   */\n  Token = 'token',\n  /**\n   * A custom registered type for getting an `id_token` from Google OAuth.\n   */\n  IdToken = 'id_token',\n}\n\n// @needsAudit\n/**\n * Informs the server if the user should be prompted to login or consent again.\n * This can be used to present a dialog for switching accounts after the user has already been logged in.\n * You should use this in favor of clearing cookies (which is mostly not possible on iOS).\n *\n * @see [Section 3.1.2.1](https://openid.net/specs/openid-connect-core-1_0.html#AuthorizationRequest).\n */\nexport enum Prompt {\n  /**\n   * Server must not display any auth or consent UI. Can be used to check for existing auth or consent.\n   * An error is returned if a user isn't already authenticated or the client doesn't have pre-configured consent for the requested claims, or does not fulfill other conditions for processing the request.\n   * The error code will typically be `login_required`, `interaction_required`, or another code defined in [Section 3.1.2.6](https://openid.net/specs/openid-connect-core-1_0.html#AuthError).\n   */\n  None = 'none',\n  /**\n   * The server should prompt the user to reauthenticate.\n   * If it cannot reauthenticate the End-User, it must return an error, typically `login_required`.\n   */\n  Login = 'login',\n  /**\n   * Server should prompt the user for consent before returning information to the client.\n   * If it cannot obtain consent, it must return an error, typically `consent_required`.\n   */\n  Consent = 'consent',\n  /**\n   * Server should prompt the user to select an account. Can be used to switch accounts.\n   * If it can't obtain an account selection choice made by the user, it must return an error, typically `account_selection_required`.\n   */\n  SelectAccount = 'select_account',\n}\n\n// @needsAudit\n/**\n * Options passed to the `promptAsync()` method of `AuthRequest`s.\n * This can be used to configure how the web browser should look and behave.\n */\nexport type AuthRequestPromptOptions = Omit<WebBrowserOpenOptions, 'windowFeatures'> & {\n  /**\n   * URL to open when prompting the user. This usually should be defined internally and left `undefined` in most cases.\n   */\n  url?: string;\n  /**\n   * Features to use with `window.open()`.\n   * @platform web\n   */\n  windowFeatures?: WebBrowserWindowFeatures;\n};\n\n// @needsAudit\n/**\n * Represents an OAuth authorization request as JSON.\n */\nexport type AuthRequestConfig = {\n  /**\n   * Specifies what is returned from the authorization server.\n   *\n   * [Section 3.1.1](https://tools.ietf.org/html/rfc6749#section-3.1.1)\n   *\n   * @default ResponseType.Code\n   */\n  responseType?: ResponseType | string;\n  /**\n   * A unique string representing the registration information provided by the client.\n   * The client identifier is not a secret; it is exposed to the resource owner and shouldn't be used\n   * alone for client authentication.\n   *\n   * The client identifier is unique to the authorization server.\n   *\n   * [Section 2.2](https://tools.ietf.org/html/rfc6749#section-2.2)\n   */\n  clientId: string;\n  /**\n   * After completing an interaction with a resource owner the\n   * server will redirect to this URI. Learn more about [linking in Expo](/guides/linking/).\n   *\n   * [Section 3.1.2](https://tools.ietf.org/html/rfc6749#section-3.1.2)\n   */\n  redirectUri: string;\n  /**\n   * List of strings to request access to.\n   *\n   * [Section 3.3](https://tools.ietf.org/html/rfc6749#section-3.3)\n   */\n  scopes?: string[];\n  /**\n   * Client secret supplied by an auth provider.\n   * There is no secure way to store this on the client.\n   *\n   * [Section 2.3.1](https://tools.ietf.org/html/rfc6749#section-2.3.1)\n   */\n  clientSecret?: string;\n  /**\n   * Method used to generate the code challenge. You should never use `Plain` as it's not good enough for secure verification.\n   * @default CodeChallengeMethod.S256\n   */\n  codeChallengeMethod?: CodeChallengeMethod;\n  /**\n   * Derived from the code verifier by using the `CodeChallengeMethod`.\n   *\n   * [Section 4.2](https://tools.ietf.org/html/rfc7636#section-4.2)\n   */\n  codeChallenge?: string;\n  /**\n   * Informs the server if the user should be prompted to login or consent again.\n   * This can be used to present a dialog for switching accounts after the user has already been logged in.\n   *\n   * [Section 3.1.2.1](https://openid.net/specs/openid-connect-core-1_0.html#AuthorizationRequest)\n   */\n  prompt?: Prompt | Prompt[];\n  /**\n   * Used for protection against [Cross-Site Request Forgery](https://tools.ietf.org/html/rfc6749#section-10.12).\n   */\n  state?: string;\n  /**\n   * Extra query params that'll be added to the query string.\n   */\n  extraParams?: Record<string, string>;\n  /**\n   * Should use [Proof Key for Code Exchange](https://oauth.net/2/pkce/).\n   * @default true\n   */\n  usePKCE?: boolean;\n};\n\nexport type AuthDiscoveryDocument = Pick<DiscoveryDocument, 'authorizationEndpoint'>;\n"]}