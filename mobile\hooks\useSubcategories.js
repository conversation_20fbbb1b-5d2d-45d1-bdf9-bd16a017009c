import { useState, useEffect, useCallback } from 'react';
import { API_URL } from '../constants/api';

export const useSubcategories = () => {
  const [subcategories, setSubcategories] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);

  const fetchSubcategories = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);
      console.log('Fetching subcategories from:', `${API_URL}/subcategories`);
      const response = await fetch(`${API_URL}/subcategories`);
      
      console.log('Subcategories response status:', response.status);
      if (!response.ok) {
        throw new Error(`Failed to fetch subcategories: ${response.status}`);
      }
      
      const data = await response.json();
      console.log('Subcategories data:', data);
      setSubcategories(data);
    } catch (err) {
      setError(err.message);
      console.error('Error fetching subcategories:', err);
    } finally {
      setIsLoading(false);
    }
  }, []);

  const getSubcategoriesByCategory = useCallback((categoryId) => {
    return subcategories.filter(subcat => subcat.category_id === categoryId);
  }, [subcategories]);

  const fetchSubcategoriesByCategory = useCallback(async (categoryId) => {
    try {
      setIsLoading(true);
      setError(null);
      const response = await fetch(`${API_URL}/subcategories/category/${categoryId}`);
      
      if (!response.ok) {
        throw new Error(`Failed to fetch subcategories for category: ${response.status}`);
      }
      
      const data = await response.json();
      return data;
    } catch (err) {
      setError(err.message);
      console.error('Error fetching subcategories by category:', err);
      return [];
    } finally {
      setIsLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchSubcategories();
  }, [fetchSubcategories]);

  return {
    subcategories,
    isLoading,
    error,
    fetchSubcategories,
    getSubcategoriesByCategory,
    fetchSubcategoriesByCategory,
  };
};
