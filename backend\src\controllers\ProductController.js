/**
 * Product Controller
 * Application layer - HTTP request handling for product operations
 */


const { HttpStatus } = require('../../shared/enums');
const { PAGINATION } = require('../../shared/constants');

class ProductController {
  constructor(productService) {
    this.productService = productService;
  }

  /**
   * Get all products
   */
  async getAllProducts(req, res, next) {
    try {
      console.log('🔥 ProductController.getAllProducts called');
      console.log('🔥 Query params:', req.query);
      console.log('🔥 ProductService available:', !!this.productService);

      const {
        status,
        categoryId,
        sellerId,
        featured,
        inStock,
        minPrice,
        maxPrice,
        sortBy = 'createdAt',
        sortOrder = 'desc',
        page = PAGINATION.DEFAULT_PAGE,
        limit = PAGINATION.DEFAULT_LIMIT,
        search
      } = req.query;

      const options = {
        page: parseInt(page),
        limit: Math.min(parseInt(limit), PAGINATION.MAX_LIMIT),
        sortBy,
        sortOrder,
        status,
        categoryId,
        sellerId,
        featured: featured !== undefined ? featured === 'true' : undefined,
        inStock: inStock === 'true',
        minPrice: minPrice ? parseFloat(minPrice) : undefined,
        maxPrice: maxPrice ? parseFloat(maxPrice) : undefined
      };

      console.log('🔥 Calling productService.getAllProducts with options:', options);
      const result = await this.productService.getAllProducts(options);
      console.log('🔥 ProductService result:', result);

      res.status(HttpStatus.OK).json({
        success: true,
        data: result,
        message: 'Products retrieved successfully'
      });
    } catch (error) {
      console.error('❌ ProductController.getAllProducts error:', error);
      next(error);
    }
  }

  /**
   * Get product by ID
   */
  async getProductById(req, res, next) {
    try {
      const { id } = req.params;
      const product = await this.productService.getProductById(id);

      res.status(HttpStatus.OK).json({
        success: true,
        data: { product }
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Create new product
   */
  async createProduct(req, res, next) {
    try {
      console.log('🔥 ProductController.createProduct called');
      console.log('🔥 Request body:', req.body);
      console.log('🔥 User ID:', req.user?.id);

      const productData = {
        ...req.body,
        sellerId: req.user.id
      };

      console.log('🔥 Product data to create:', productData);
      const product = await this.productService.createProduct(productData, req.user.id);
      console.log('🔥 Created product:', product);

      res.status(HttpStatus.CREATED).json({
        success: true,
        message: 'Product created successfully',
        data: { product }
      });
    } catch (error) {
      console.error('❌ ProductController.createProduct error:', error);
      next(error);
    }
  }

  /**
   * Update product
   */
  async updateProduct(req, res, next) {
    try {
      console.log('🔥 ProductController.updateProduct called');
      console.log('🔥 Product ID:', req.params.id);
      console.log('🔥 Request body:', req.body);
      console.log('🔥 User ID:', req.user?.id);

      const { id } = req.params;
      const product = await this.productService.updateProduct(id, req.body, req.user.id);
      console.log('🔥 Updated product:', product);

      res.status(HttpStatus.OK).json({
        success: true,
        message: 'Product updated successfully',
        data: { product }
      });
    } catch (error) {
      console.error('❌ ProductController.updateProduct error:', error);
      next(error);
    }
  }

  /**
   * Delete product
   */
  async deleteProduct(req, res, next) {
    try {
      const { id } = req.params;
      await this.productService.deleteProduct(id, req.user.id);

      res.status(HttpStatus.OK).json({
        success: true,
        message: 'Product deleted successfully'
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get products by category
   */
  async getProductsByCategory(req, res, next) {
    try {
      const { categoryId } = req.params;
      const {
        page = PAGINATION.DEFAULT_PAGE,
        limit = PAGINATION.DEFAULT_LIMIT,
        sortBy = 'createdAt',
        sortOrder = 'desc'
      } = req.query;

      const options = {
        page: parseInt(page),
        limit: Math.min(parseInt(limit), PAGINATION.MAX_LIMIT),
        sortBy,
        sortOrder
      };

      const result = await this.productService.getProductsByCategory(categoryId, options);

      res.status(HttpStatus.OK).json({
        success: true,
        data: result
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get featured products
   */
  async getFeaturedProducts(req, res, next) {
    try {
      const { limit = 10 } = req.query;
      const products = await this.productService.getFeaturedProducts({ limit: parseInt(limit) });

      res.status(HttpStatus.OK).json({
        success: true,
        data: { products }
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get user's products (seller dashboard)
   */
  async getUserProducts(req, res, next) {
    try {
      const {
        status,
        page = PAGINATION.DEFAULT_PAGE,
        limit = PAGINATION.DEFAULT_LIMIT,
        sortBy = 'createdAt',
        sortOrder = 'desc'
      } = req.query;

      const options = {
        page: parseInt(page),
        limit: Math.min(parseInt(limit), PAGINATION.MAX_LIMIT),
        sortBy,
        sortOrder,
        status
      };

      const result = await this.productService.getProductsBySeller(req.user.id, options);

      res.status(HttpStatus.OK).json({
        success: true,
        data: result
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Approve product (Admin)
   */
  async approveProduct(req, res, next) {
    try {
      const { id } = req.params;
      const { reason } = req.body;

      const product = await this.productService.approveProduct(id, req.user.id, reason);

      res.status(HttpStatus.OK).json({
        success: true,
        message: 'Product approved successfully',
        data: { product }
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Reject product (Admin)
   */
  async rejectProduct(req, res, next) {
    try {
      const { id } = req.params;
      const { reason } = req.body;

      if (!reason) {
        return res.status(HttpStatus.BAD_REQUEST).json({
          success: false,
          message: 'Rejection reason is required'
        });
      }

      const product = await this.productService.rejectProduct(id, req.user.id, reason);

      res.status(HttpStatus.OK).json({
        success: true,
        message: 'Product rejected successfully',
        data: { product }
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Update product status (Admin)
   */
  async updateProductStatus(req, res, next) {
    try {
      const { id } = req.params;
      const { status } = req.body;

      if (!status) {
        return res.status(HttpStatus.BAD_REQUEST).json({
          success: false,
          message: 'Status is required'
        });
      }

      const product = await this.productService.updateProductStatus(id, status, req.user.id);

      res.status(HttpStatus.OK).json({
        success: true,
        message: 'Product status updated successfully',
        data: { product }
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Update product inventory
   */
  async updateInventory(req, res, next) {
    try {
      const { id } = req.params;
      const { quantity } = req.body;

      if (quantity === undefined || quantity < 0) {
        return res.status(HttpStatus.BAD_REQUEST).json({
          success: false,
          message: 'Valid quantity is required'
        });
      }

      const product = await this.productService.updateInventory(id, parseInt(quantity), req.user.id);

      res.status(HttpStatus.OK).json({
        success: true,
        message: 'Inventory updated successfully',
        data: { product }
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Check product availability
   */
  async checkAvailability(req, res, next) {
    try {
      const { id } = req.params;
      const { quantity = 1 } = req.query;

      const available = await this.productService.checkAvailability(id, parseInt(quantity));

      res.status(HttpStatus.OK).json({
        success: true,
        data: { available }
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get product statistics
   */
  async getProductStatistics(req, res, next) {
    try {
      const { id } = req.params;
      const statistics = await this.productService.getProductStatistics(id);

      res.status(HttpStatus.OK).json({
        success: true,
        data: { statistics }
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Increment product view count
   */
  async incrementViewCount(req, res, next) {
    try {
      const { id } = req.params;
      await this.productService.incrementViewCount(id);

      res.status(HttpStatus.OK).json({
        success: true,
        message: 'View count updated'
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get low stock products (Admin/Seller)
   */
  async getLowStockProducts(req, res, next) {
    try {
      const { threshold = 10 } = req.query;
      const products = await this.productService.getLowStockProducts(parseInt(threshold));

      res.status(HttpStatus.OK).json({
        success: true,
        data: { products }
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get product counts by status (Admin)
   */
  async getProductCounts(req, res, next) {
    try {
      const { sellerId } = req.query;
      const counts = await this.productService.getProductCounts(sellerId);

      res.status(HttpStatus.OK).json({
        success: true,
        data: { counts }
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get related products
   */
  async getRelatedProducts(req, res, next) {
    try {
      const { id } = req.params;
      const { limit = 5 } = req.query;

      const products = await this.productService.getRelatedProducts(id, parseInt(limit));

      res.status(HttpStatus.OK).json({
        success: true,
        data: { products }
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Bulk update products (Admin)
   */
  async bulkUpdateProducts(req, res, next) {
    try {
      const { updates } = req.body;

      if (!updates || !Array.isArray(updates)) {
        return res.status(HttpStatus.BAD_REQUEST).json({
          success: false,
          message: 'Updates array is required'
        });
      }

      const result = await this.productService.bulkUpdateProducts(updates, req.user.id);

      res.status(HttpStatus.OK).json({
        success: true,
        message: 'Products updated successfully',
        data: result
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Search products
   */
  async searchProducts(req, res, next) {
    try {
      const { q: query } = req.query;

      if (!query) {
        return res.status(HttpStatus.BAD_REQUEST).json({
          success: false,
          message: 'Search query is required'
        });
      }

      const {
        categoryId,
        minPrice,
        maxPrice,
        sortBy = 'relevance',
        sortOrder = 'desc',
        page = PAGINATION.DEFAULT_PAGE,
        limit = PAGINATION.DEFAULT_LIMIT
      } = req.query;

      const options = {
        page: parseInt(page),
        limit: Math.min(parseInt(limit), PAGINATION.MAX_LIMIT),
        sortBy,
        sortOrder,
        categoryId,
        minPrice: minPrice ? parseFloat(minPrice) : undefined,
        maxPrice: maxPrice ? parseFloat(maxPrice) : undefined
      };

      const result = await this.productService.searchProducts(query, options);

      res.status(HttpStatus.OK).json({
        success: true,
        data: result
      });
    } catch (error) {
      next(error);
    }
  }
}

module.exports = ProductController;
