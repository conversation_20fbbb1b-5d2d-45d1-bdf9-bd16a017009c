import  rateLimit from "../config/upstash.js";

const rateLimiter = async (req, res, next) => {

    try {

        const {success} = await rateLimit.limit("my rate limit");
        if (!success) {
            return res.status(429).json({
                message:"Too many requests please try again later"
            });
        }
        next();
    } catch (error) {
        console.log("Error in rate limiter", error);
        res.status(500).json({message:"internal server error"});
    }
}

export default rateLimiter;