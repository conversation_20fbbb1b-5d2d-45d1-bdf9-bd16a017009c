import React from 'react';
import { ViewProps } from '../primitives/View';
export declare const Table: React.ComponentType<ViewProps>;
export declare const THead: React.ComponentType<ViewProps>;
export declare const TBody: React.ComponentType<ViewProps>;
export declare const TFoot: React.ComponentType<ViewProps>;
export declare const TH: React.ComponentType<ViewProps>;
export declare const TR: React.ComponentType<ViewProps>;
export declare const TD: React.ComponentType<ViewProps>;
export declare const Caption: React.ComponentType<ViewProps>;
//# sourceMappingURL=Table.web.d.ts.map