{"version": 3, "file": "Rules.web.js", "sourceRoot": "", "sources": ["../../src/elements/Rules.web.tsx"], "names": [], "mappings": "AAAA,OAAO,EAAiB,UAAU,EAAE,MAAM,OAAO,CAAC;AAClD,OAAO,aAAa,MAAM,6CAA6C,CAAC;AAIxE,MAAM,CAAC,MAAM,EAAE,GAAG,UAAU,CAAC,CAAC,KAAgB,EAAE,GAAG,EAAE,EAAE;IACrD,OAAO,aAAa,CAAC,IAAI,EAAE,EAAE,GAAG,KAAK,EAAE,GAAG,EAAE,CAAC,CAAC;AAChD,CAAC,CAA6B,CAAC", "sourcesContent": ["import { ComponentType, forwardRef } from 'react';\nimport createElement from 'react-native-web/dist/exports/createElement';\n\nimport { ViewProps } from '../primitives/View';\n\nexport const HR = forwardRef((props: ViewProps, ref) => {\n  return createElement('hr', { ...props, ref });\n}) as ComponentType<ViewProps>;\n"]}