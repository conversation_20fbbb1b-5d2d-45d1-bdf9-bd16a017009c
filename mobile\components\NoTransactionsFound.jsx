    import React from 'react'
import { View, Text, TouchableOpacity } from 'react-native'
import {styles} from '../assets/styles/home.styles'
import { COLORS } from '../constants/colors'
import { Ionicons } from '@expo/vector-icons'
import { useRouter } from 'expo-router'

export const NoTransactionsFound = () => {
  const router = useRouter();

  return (
    <View style={styles.emptyState}>

      <Ionicons 
      name="receipt-outline" 
      size={60} 
      color={COLORS.textLight}
      style={styles.emptyStateIcon}
       />

      <Text style={styles.emptyStateTitle}>No transactions found</Text>
      <Text style={styles.emptyStateText}>
            strat tracking your finances today!
      </Text>
      <TouchableOpacity style={styles.emptyStateButton} onPress={() => router.push('/create')}>
        <Ionicons name="add-circle-outline" size={24} color={COLORS.white} />
        <Text style={styles.emptyStateButtonText}>Add Transaction</Text>
      </TouchableOpacity>
    </View>
  )
}






