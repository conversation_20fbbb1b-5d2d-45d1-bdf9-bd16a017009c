import express from "express";
import { sql } from "../config/db.js";
import bcrypt from 'bcrypt';

const router = express.Router();

// Get all users
router.get('/', async (req, res) => {
    try {
        const users = await sql`
            SELECT id, name, email, username, role, status, approval_status, 
                   is_email_verified, can_bid, can_participate_in_lottery, 
                   profile, created_at, updated_at
            FROM users 
            ORDER BY created_at DESC
        `;
        res.status(200).json(users);
    } catch (error) {
        console.error('Error fetching users:', error);
        res.status(500).json({ message: "Internal server error" });
    }
});

// Get user by ID
router.get('/:id', async (req, res) => {
    try {
        const { id } = req.params;
        
        if (isNaN(parseInt(id))) {
            return res.status(400).json({ message: "Invalid user ID" });
        }
        
        const user = await sql`
            SELECT id, name, email, username, role, status, approval_status, 
                   is_email_verified, can_bid, can_participate_in_lottery, 
                   profile, created_at, updated_at
            FROM users 
            WHERE id = ${id}
        `;
        
        if (user.length === 0) {
            return res.status(404).json({ message: "User not found" });
        }
        
        res.status(200).json(user[0]);
    } catch (error) {
        console.error('Error fetching user:', error);
        res.status(500).json({ message: "Internal server error" });
    }
});

// Create new user
router.post('/', async (req, res) => {
    try {
        const {
            name,
            email,
            username,
            password,
            role = 'user',
            status = 'active',
            approval_status = 'approved',
            is_email_verified = false,
            can_bid = false,
            can_participate_in_lottery = false,
            profile = {}
        } = req.body;

        // Validation
        if (!name || !email || !username || !password) {
            return res.status(400).json({ message: 'Name, email, username, and password are required' });
        }

        // Check if user already exists
        const existingUser = await sql`
            SELECT * FROM users WHERE email = ${email} OR username = ${username}
        `;

        if (existingUser.length > 0) {
            return res.status(400).json({ message: 'User with this email or username already exists' });
        }

        // Hash password
        const saltRounds = 10;
        const hashedPassword = await bcrypt.hash(password, saltRounds);

        const user = await sql`
            INSERT INTO users (
                name, email, username, password, role, status, approval_status,
                is_email_verified, can_bid, can_participate_in_lottery, profile
            ) VALUES (
                ${name}, ${email}, ${username}, ${hashedPassword}, ${role}, ${status}, ${approval_status},
                ${is_email_verified}, ${can_bid}, ${can_participate_in_lottery}, ${JSON.stringify(profile)}
            )
            RETURNING id, name, email, username, role, status, approval_status, 
                      is_email_verified, can_bid, can_participate_in_lottery, 
                      profile, created_at, updated_at
        `;

        res.status(201).json(user[0]);
    } catch (error) {
        console.error('Error creating user:', error);
        res.status(500).json({ message: "Internal server error" });
    }
});

// Update user
router.put('/:id', async (req, res) => {
    try {
        const { id } = req.params;
        const {
            name,
            email,
            username,
            role,
            status,
            approval_status,
            is_email_verified,
            can_bid,
            can_participate_in_lottery,
            profile
        } = req.body;

        if (isNaN(parseInt(id))) {
            return res.status(400).json({ message: "Invalid user ID" });
        }

        // Check if user exists
        const existingUser = await sql`
            SELECT * FROM users WHERE id = ${id}
        `;

        if (existingUser.length === 0) {
            return res.status(404).json({ message: "User not found" });
        }

        // Check if another user with the same email or username exists
        if (email || username) {
            const duplicateUser = await sql`
                SELECT * FROM users 
                WHERE (email = ${email || existingUser[0].email} OR username = ${username || existingUser[0].username}) 
                AND id != ${id}
            `;

            if (duplicateUser.length > 0) {
                return res.status(400).json({ message: 'Email or username already exists' });
            }
        }

        const updatedUser = await sql`
            UPDATE users 
            SET 
                name = ${name || existingUser[0].name},
                email = ${email || existingUser[0].email},
                username = ${username || existingUser[0].username},
                role = ${role || existingUser[0].role},
                status = ${status || existingUser[0].status},
                approval_status = ${approval_status || existingUser[0].approval_status},
                is_email_verified = ${is_email_verified !== undefined ? is_email_verified : existingUser[0].is_email_verified},
                can_bid = ${can_bid !== undefined ? can_bid : existingUser[0].can_bid},
                can_participate_in_lottery = ${can_participate_in_lottery !== undefined ? can_participate_in_lottery : existingUser[0].can_participate_in_lottery},
                profile = ${profile ? JSON.stringify(profile) : existingUser[0].profile},
                updated_at = CURRENT_DATE
            WHERE id = ${id}
            RETURNING id, name, email, username, role, status, approval_status, 
                      is_email_verified, can_bid, can_participate_in_lottery, 
                      profile, created_at, updated_at
        `;

        res.status(200).json(updatedUser[0]);
    } catch (error) {
        console.error('Error updating user:', error);
        res.status(500).json({ message: "Internal server error" });
    }
});

// Delete user
router.delete('/:id', async (req, res) => {
    try {
        const { id } = req.params;

        if (isNaN(parseInt(id))) {
            return res.status(400).json({ message: "Invalid user ID" });
        }

        // Check if user has products or transactions
        const userProducts = await sql`
            SELECT COUNT(*) as count FROM products WHERE seller_id = ${id}
        `;

        const userTransactions = await sql`
            SELECT COUNT(*) as count FROM transactions WHERE user_id = ${id}
        `;

        if (userProducts[0].count > 0 || userTransactions[0].count > 0) {
            return res.status(400).json({ 
                message: "Cannot delete user with existing products or transactions" 
            });
        }

        const result = await sql`
            DELETE FROM users WHERE id = ${id} RETURNING *
        `;

        if (result.length === 0) {
            return res.status(404).json({ message: "User not found" });
        }

        res.status(200).json({ message: "User deleted successfully" });
    } catch (error) {
        console.error('Error deleting user:', error);
        res.status(500).json({ message: "Internal server error" });
    }
});

// Get user's products
router.get('/:id/products', async (req, res) => {
    try {
        const { id } = req.params;
        
        if (isNaN(parseInt(id))) {
            return res.status(400).json({ message: "Invalid user ID" });
        }

        const products = await sql`
            SELECT * FROM products 
            WHERE seller_id = ${id}
            ORDER BY created_at DESC
        `;
        
        res.status(200).json(products);
    } catch (error) {
        console.error('Error fetching user products:', error);
        res.status(500).json({ message: "Internal server error" });
    }
});

// Get user's transactions
router.get('/:id/transactions', async (req, res) => {
    try {
        const { id } = req.params;
        
        if (isNaN(parseInt(id))) {
            return res.status(400).json({ message: "Invalid user ID" });
        }

        const transactions = await sql`
            SELECT * FROM transactions 
            WHERE user_id = ${id}
            ORDER BY created_at DESC
        `;
        
        res.status(200).json(transactions);
    } catch (error) {
        console.error('Error fetching user transactions:', error);
        res.status(500).json({ message: "Internal server error" });
    }
});

export default router;
