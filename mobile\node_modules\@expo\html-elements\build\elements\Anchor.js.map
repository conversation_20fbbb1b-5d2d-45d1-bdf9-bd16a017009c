{"version": 3, "file": "Anchor.js", "sourceRoot": "", "sources": ["../../src/elements/Anchor.tsx"], "names": [], "mappings": "AAAA,OAAO,KAAK,EAAE,EAAiB,UAAU,EAAE,MAAM,OAAO,CAAC;AACzD,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAE,MAAM,cAAc,CAAC;AAEjD,OAAO,IAAI,MAAM,oBAAoB,CAAC;AAGtC,MAAM,CAAC,MAAM,CAAC,GAAG,UAAU,CAAC,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,GAAG,EAAE,GAAG,KAAK,EAAa,EAAE,GAAG,EAAE,EAAE;IACxF,MAAM,WAAW,GAAG,QAAQ,CAAC,MAAM,CAAY;QAC7C,GAAG,EAAE;YACH,IAAI;YACJ,SAAS,EAAE;gBACT,MAAM;gBACN,QAAQ;gBACR,GAAG;aACJ;SACF;QACD,OAAO,EAAE;YACP,OAAO,EAAE,CAAC,KAAK,EAAE,EAAE;gBACjB,KAAK,CAAC,OAAO,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;gBACtC,IAAI,QAAQ,CAAC,EAAE,KAAK,KAAK,IAAI,IAAI,KAAK,SAAS,EAAE;oBAC/C,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;iBACvB;YACH,CAAC;SACF;KACF,CAAC,CAAC;IACH,OAAO,oBAAC,IAAI,IAAC,iBAAiB,EAAC,MAAM,KAAK,KAAK,KAAM,WAAW,EAAE,GAAG,EAAE,GAAG,GAAI,CAAC;AACjF,CAAC,CAA6B,CAAC", "sourcesContent": ["import React, { ComponentType, forwardRef } from 'react';\nimport { Linking, Platform } from 'react-native';\n\nimport Text from '../primitives/Text';\nimport { LinkProps } from './Text.types';\n\nexport const A = forwardRef(({ href, target, download, rel, ...props }: LinkProps, ref) => {\n  const nativeProps = Platform.select<LinkProps>({\n    web: {\n      href,\n      hrefAttrs: {\n        target,\n        download,\n        rel,\n      },\n    },\n    default: {\n      onPress: (event) => {\n        props.onPress && props.onPress(event);\n        if (Platform.OS !== 'web' && href !== undefined) {\n          Linking.openURL(href);\n        }\n      },\n    },\n  });\n  return <Text accessibilityRole=\"link\" {...props} {...nativeProps} ref={ref} />;\n}) as ComponentType<LinkProps>;\n"]}