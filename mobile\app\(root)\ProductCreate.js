    import { View, Text, Alert, ActivityIndicator, TextInput, TouchableOpacity} from 'react-native'
    import {styles} from '../../assets/styles/create.styles'
    import { useRouter } from 'expo-router'
    import { useState } from 'react'
    import { API_URL } from '../../constants/api'
    import { useUser } from '@clerk/clerk-expo'
    import { Ionicons } from '@expo/vector-icons'
    import { COLORS } from '../../constants/colors'
    
    const ProductCreateScreen = () => {
        const router = useRouter();
        const {user} = useUser();
        const [name, setName] = useState('');
        const [description, setDescription] = useState('');
        const [price, setPrice] = useState('');
        const [originalPrice, setOriginalPrice] = useState('');
        const [discountPercentage, setDiscountPercentage] = useState('');
        const [images, setImages] = useState([]);
        const [category, setCategory] = useState('');
        const [stock, setStock] = useState('');
        const [featured, setFeatured] = useState(false);
        const [status, setStatus] = useState('active');
        const [specifications, setSpecifications] = useState({});
        const [tags, setTags] = useState([]);
        const [dynamicPricing, setDynamicPricing] = useState({});
        const [isLoading, setIsLoading] = useState(false);
    
        const handleCreate = async () => {
            if (!name.trim() ) return Alert.alert("Error", "Please enter a name");
            if (!description.trim() ) return Alert.alert("Error", "Please enter a description");
            if (!price || 	isNaN(parseFloat(price)) || parseFloat(price <= 0) ) return Alert.alert("Error", "Please enter a price greater than 0");
            if (!originalPrice || 	isNaN(parseFloat(originalPrice)) || parseFloat(originalPrice <= 0) ) return Alert.alert("Error", "Please enter an original price greater than 0");
            if (!discountPercentage || 	isNaN(parseFloat(discountPercentage)) || parseFloat(discountPercentage < 0) || parseFloat(discountPercentage > 100) ) return Alert.alert("Error", "Please enter a discount percentage between 0 and 100");
            if (!category.trim() ) return Alert.alert("Error", "Please enter a category");
            if (!stock || 	isNaN(parseFloat(stock)) || parseFloat(stock < 0) ) return Alert.alert("Error", "Please enter a stock greater than or equal to 0");
    
            setIsLoading(true);
            try {
                const response = await fetch(`${API_URL}/products`, {
                    method: 'POST',
                    headers: {
                      'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                      name,
                      description,
                      price,
                      original_price: originalPrice,
                      discount_percentage: discountPercentage,
                      images,
                      category,
                      stock,
                      featured,
                      status,
                      seller_id: user.id,
                      specifications,
                      tags,
                      dynamic_pricing: dynamicPricing,
                    }),
                  });
                  if (!response.ok) {
                    throw new Error('Failed to create product');
                  }
                  await response.json();
                  router.back();
              } catch (error) {
                console.error('Error creating product', error);
              } finally {
                setIsLoading(false);
              }
            }
    
        return (
            <View style={styles.container}>
                <View style={styles.header}>
                    <TouchableOpacity style={styles.backButton} onPress={() => router.back()}>
                        <Ionicons name="arrow-back" size={24} color={COLORS.text} />
                    </TouchableOpacity>
                    <Text style={styles.headerTitle}>New Product</Text>
                    <TouchableOpacity
                    style={[styles.saveButtonContainer, !name.trim() || !description.trim() || !price.trim() || !originalPrice.trim() || !category.trim() || !stock.trim() ? styles.saveButtonDisabled : null]}
                    onPress={handleCreate}
                    disabled={!name.trim() || !description.trim() || !price.trim() || !originalPrice.trim() || !category.trim() || !stock.trim()}
                    >
                        <Ionicons name="checkmark" size={24} color={COLORS.white} />
                        <Text style={styles.saveButton}>Save</Text>
                    </TouchableOpacity>
                </View>
                <View style={styles.card}>
                    <View style={styles.inputContainer}>
                        <Ionicons name="pricetags" size={24} color={COLORS.text} style={styles.inputIcon} />
                        <TextInput
                        style={styles.input}
                        value={name}
                        placeholder="Name"
                        placeholderTextColor={COLORS.textLight}
                        onChangeText={(name) => setName(name)}
                        />
                    </View>
                    <View style={styles.inputContainer}>
                        <Ionicons name="pricetags" size={24} color={COLORS.text} style={styles.inputIcon} />
                        <TextInput
                        style={styles.input}
                        value={description}
                        placeholder="Description"
                        placeholderTextColor={COLORS.textLight}
                        onChangeText={(description) => setDescription(description)}
                        />
                    </View>
                    <View style={styles.inputContainer}>
                        <Ionicons name="pricetags" size={24} color={COLORS.text} style={styles.inputIcon} />
                        <TextInput
                        style={styles.input}
                        value={price}
                        placeholder="Price"
                        placeholderTextColor={COLORS.textLight}
                        onChangeText={(price) => setPrice(price)}
                        />
                    </View>
                    <View style={styles.inputContainer}>
                        <Ionicons name="pricetags" size={24} color={COLORS.text} style={styles.inputIcon} />
                        <TextInput
                        style={styles.input}
                        value={originalPrice}
                        placeholder="Original Price"
                        placeholderTextColor={COLORS.textLight}
                        onChangeText={(originalPrice) => setOriginalPrice(originalPrice)}
                        />
                    </View>
                    <View style={styles.inputContainer}>
                        <Ionicons name="pricetags" size={24} color={COLORS.text} style={styles.inputIcon} />
                        <TextInput
                        style={styles.input}
                        value={discountPercentage}
                        placeholder="Discount Percentage"
                        placeholderTextColor={COLORS.textLight}
                        onChangeText={(discountPercentage) => setDiscountPercentage(discountPercentage)}
                        />
                    </View>
                    <View style={styles.inputContainer}>
                        <Ionicons name="pricetags" size={24} color={COLORS.text} style={styles.inputIcon} />
                        <TextInput
                        style={styles.input}
                        value={category}
                        placeholder="Category"
                        placeholderTextColor={COLORS.textLight}
                        onChangeText={(category) => setCategory(category)}
                        />
                    </View>
                    <View style={styles.inputContainer}>
                        <Ionicons name="pricetags" size={24} color={COLORS.text} style={styles.inputIcon} />
                        <TextInput
                        style={styles.input}
                        value={stock}
                        placeholder="Stock"
                        placeholderTextColor={COLORS.textLight}
                        onChangeText={(stock) => setStock(stock)}
                        />
                    </View>
                </View>
                {isLoading && (
                    <View style={styles.loadingContainer}>
                    <ActivityIndicator size="large" color={COLORS.primary}/>
                    </View>
                )}
            </View>
        )
    }

export default ProductCreateScreen;
















