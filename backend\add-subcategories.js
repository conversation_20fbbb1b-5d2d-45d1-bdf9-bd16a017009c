import { sql } from './src/config/db.js';
import 'dotenv/config';

async function addSubcategories() {
    try {
        console.log('Adding subcategories...');

        // Get categories first
        const categories = await sql`SELECT * FROM categories ORDER BY id`;
        console.log('Found categories:', categories.length);

        // Subcategories data
        const subcategoriesData = [
            // Elektronik (1)
            { name: 'Telefon & Tablet', category_name: 'Elektronik' },
            { name: 'Bilgisayar & Laptop', category_name: 'Elektronik' },
            { name: 'TV & Ses Sistemleri', category_name: 'Elektronik' },
            { name: '<PERSON><PERSON>nsollar<PERSON>', category_name: 'Elektronik' },
            { name: '<PERSON><PERSON><PERSON><PERSON><PERSON>', category_name: 'Elektronik' },
            { name: 'Kamera & Fotoğraf', category_name: 'Elektronik' },

            // Giyim (2)
            { name: '<PERSON><PERSON><PERSON>', category_name: '<PERSON><PERSON><PERSON><PERSON>' },
            { name: '<PERSON><PERSON><PERSON><PERSON>', category_name: '<PERSON><PERSON><PERSON><PERSON>' },
            { name: '<PERSON><PERSON><PERSON>', category_name: '<PERSON><PERSON><PERSON><PERSON>' },
            { name: '<PERSON><PERSON><PERSON><PERSON>ı', category_name: 'Giyim' },
            { name: 'Çanta & Aksesuar', category_name: 'Giyim' },
            { name: 'Spor Giyim', category_name: 'Giyim' },

            // Ev & Yaşam (3)
            { name: 'Mobilya', category_name: 'Ev & Yaşam' },
            { name: 'Dekorasyon', category_name: 'Ev & Yaşam' },
            { name: 'Mutfak Gereçleri', category_name: 'Ev & Yaşam' },
            { name: 'Banyo & Temizlik', category_name: 'Ev & Yaşam' },
            { name: 'Bahçe & Balkon', category_name: 'Ev & Yaşam' },
            { name: 'Ev Tekstili', category_name: 'Ev & Yaşam' },

            // Spor & Outdoor (4)
            { name: 'Fitness & Kondisyon', category_name: 'Spor & Outdoor' },
            { name: 'Futbol', category_name: 'Spor & Outdoor' },
            { name: 'Basketbol', category_name: 'Spor & Outdoor' },
            { name: 'Koşu & Atletizm', category_name: 'Spor & Outdoor' },
            { name: 'Kamp & Doğa', category_name: 'Spor & Outdoor' },
            { name: 'Su Sporları', category_name: 'Spor & Outdoor' },

            // Kitap & Hobi (5)
            { name: 'Roman & Edebiyat', category_name: 'Kitap & Hobi' },
            { name: 'Bilim & Teknik', category_name: 'Kitap & Hobi' },
            { name: 'Çocuk Kitapları', category_name: 'Kitap & Hobi' },
            { name: 'Sanat & Tasarım', category_name: 'Kitap & Hobi' },
            { name: 'Müzik Aletleri', category_name: 'Kitap & Hobi' },
            { name: 'Oyuncak & Oyun', category_name: 'Kitap & Hobi' },

            // Otomotiv (6)
            { name: 'Otomobil', category_name: 'Otomotiv' },
            { name: 'Motosiklet', category_name: 'Otomotiv' },
            { name: 'Yedek Parça', category_name: 'Otomotiv' },
            { name: 'Aksesuar', category_name: 'Otomotiv' },
            { name: 'Lastik & Jant', category_name: 'Otomotiv' },
            { name: 'Bakım & Onarım', category_name: 'Otomotiv' },

            // Sağlık & Güzellik (7)
            { name: 'Cilt Bakımı', category_name: 'Sağlık & Güzellik' },
            { name: 'Makyaj', category_name: 'Sağlık & Güzellik' },
            { name: 'Saç Bakımı', category_name: 'Sağlık & Güzellik' },
            { name: 'Parfüm & Deodorant', category_name: 'Sağlık & Güzellik' },
            { name: 'Sağlık Ürünleri', category_name: 'Sağlık & Güzellik' },
            { name: 'Fitness & Beslenme', category_name: 'Sağlık & Güzellik' },
        ];

        // Insert subcategories
        for (const subcat of subcategoriesData) {
            // Find category ID
            const category = categories.find(c => c.name === subcat.category_name);
            if (category) {
                try {
                    await sql`
                        INSERT INTO subcategories (name, category_id) 
                        VALUES (${subcat.name}, ${category.id})
                    `;
                    console.log(`✓ Added subcategory: ${subcat.name} -> ${subcat.category_name}`);
                } catch (error) {
                    if (error.message.includes('duplicate')) {
                        console.log(`- Subcategory already exists: ${subcat.name}`);
                    } else {
                        console.error(`Error adding subcategory ${subcat.name}:`, error.message);
                    }
                }
            } else {
                console.log(`! Category not found: ${subcat.category_name}`);
            }
        }

        console.log('\n✅ Subcategories added successfully!');
        
        // Show summary
        const totalSubcategories = await sql`SELECT COUNT(*) as count FROM subcategories`;
        console.log(`📊 Total subcategories in database: ${totalSubcategories[0].count}`);

    } catch (error) {
        console.error('❌ Error adding subcategories:', error);
    } finally {
        process.exit(0);
    }
}

addSubcategories();
