{"version": 3, "file": "View.js", "sourceRoot": "", "sources": ["../../src/primitives/View.tsx"], "names": [], "mappings": "AACA,OAAO,EAGL,IAAI,IAAI,UAAU,GAEnB,MAAM,cAAc,CAAC;AAEtB,OAAO,EAAE,oBAAoB,EAAE,MAAM,6BAA6B,CAAC;AACnE,OAAO,EAAE,aAAa,EAAE,MAAM,iBAAiB,CAAC;AA0IhD,IAAI,IAAI,GAAG,UAAsC,CAAC;AAElD,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,YAAY,EAAE;IACzC,wDAAwD;IACxD,IAAI,GAAG,aAAa,CAAC,UAAU,CAA6B,CAAC;CAC9D;AAED,eAAe,oBAAoB,CAAC,IAAI,CAA6B,CAAC", "sourcesContent": ["import { ClassAttributes, ComponentProps, ComponentType } from 'react';\nimport {\n  AccessibilityRole,\n  StyleProp,\n  View as NativeView,\n  ViewStyle as NativeViewStyle,\n} from 'react-native';\n\nimport { createSafeStyledView } from '../css/createSafeStyledView';\nimport { createDevView } from './createDevView';\n\n// https://github.com/necolas/react-native-web/issues/832\n\ntype NativeViewProps = ComponentProps<typeof NativeView> & ClassAttributes<typeof NativeView>;\n\n/**\n * https://baconbrix.gitbook.io/react-native-web/primitives/view\n */\nexport interface WebViewStyle {\n  /** @platform web */\n  backdropFilter?: string;\n  /** @platform web */\n  animationDelay?: string;\n  /** @platform web */\n  animationDirection?: string;\n  /** @platform web */\n  animationDuration?: string;\n  /** @platform web */\n  animationFillMode?: string;\n  /** @platform web */\n  animationName?: string | any[];\n  /** @platform web */\n  animationIterationCount?: number | 'infinite';\n  /** @platform web */\n  animationPlayState?: string;\n  /** @platform web */\n  animationTimingFunction?: string;\n  /** @platform web */\n  backgroundAttachment?: string;\n  /** @platform web */\n  backgroundBlendMode?: string;\n  /** @platform web */\n  backgroundClip?: string;\n  /** @platform web */\n  backgroundImage?: string;\n  /** @platform web */\n  backgroundOrigin?: 'border-box' | 'content-box' | 'padding-box';\n  /** @platform web */\n  backgroundPosition?: string;\n  /** @platform web */\n  backgroundRepeat?: string;\n  /** @platform web */\n  backgroundSize?: string;\n  /** @platform web */\n  boxShadow?: string;\n  /** @platform web */\n  boxSizing?: string;\n  /** @platform web */\n  clip?: string;\n  /** @platform web */\n  cursor?: string;\n  /** @platform web */\n  filter?: string;\n  /** @platform web */\n  gridAutoColumns?: string;\n  /** @platform web */\n  gridAutoFlow?: string;\n  /** @platform web */\n  gridAutoRows?: string;\n  /** @platform web */\n  gridColumnEnd?: string;\n  /** @platform web */\n  gridColumnGap?: string;\n  /** @platform web */\n  gridColumnStart?: string;\n  /** @platform web */\n  gridRowEnd?: string;\n  /** @platform web */\n  gridRowGap?: string;\n  /** @platform web */\n  gridRowStart?: string;\n  /** @platform web */\n  gridTemplateColumns?: string;\n  /** @platform web */\n  gridTemplateRows?: string;\n  /** @platform web */\n  gridTemplateAreas?: string;\n  /** @platform web */\n  outline?: string;\n  /** @platform web */\n  outlineColor?: string;\n  /** @platform web */\n  overflowX?: string;\n  /** @platform web */\n  overflowY?: string;\n  /** @platform web */\n  overscrollBehavior?: 'auto' | 'contain' | 'none';\n  /** @platform web */\n  overscrollBehaviorX?: 'auto' | 'contain' | 'none';\n  /** @platform web */\n  overscrollBehaviorY?: 'auto' | 'contain' | 'none';\n  /** @platform web */\n  perspective?: string;\n  /** @platform web */\n  perspectiveOrigin?: string;\n  /** @platform web */\n  touchAction?: string;\n  /** @platform web */\n  transformOrigin?: string;\n  /** @platform web */\n  transitionDelay?: string;\n  /** @platform web */\n  transitionDuration?: string;\n  /** @platform web */\n  transitionProperty?: string;\n  /** @platform web */\n  transitionTimingFunction?: string;\n  /** @platform web */\n  userSelect?: string;\n  /** @platform web */\n  visibility?: string;\n  /** @platform web */\n  willChange?: string;\n  /** @platform web */\n  position?: 'static' | 'relative' | 'absolute' | 'fixed' | 'sticky';\n}\n\nexport type ViewStyle = Omit<NativeViewStyle, 'position'> & WebViewStyle;\n\nexport type WebViewProps = {\n  style?: StyleProp<ViewStyle>;\n\n  accessibilityRole?:\n    | 'list'\n    | 'listitem'\n    | 'complementary'\n    | 'contentinfo'\n    | 'region'\n    | 'navigation'\n    | 'main'\n    | 'article'\n    | 'banner'\n    | AccessibilityRole;\n};\n\nexport type ViewProps = WebViewProps & Omit<NativeViewProps, 'style' | 'accessibilityRole'>;\n\nlet View = NativeView as ComponentType<ViewProps>;\n\nif (process.env.NODE_ENV !== 'production') {\n  // Add better errors and warnings in development builds.\n  View = createDevView(NativeView) as ComponentType<ViewProps>;\n}\n\nexport default createSafeStyledView(View) as ComponentType<ViewProps>;\n"]}