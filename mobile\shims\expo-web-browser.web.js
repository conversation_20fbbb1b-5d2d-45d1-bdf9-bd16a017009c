// Web shim for expo-web-browser
// This provides minimal compatibility for web platform

export const WebBrowserResultType = {
  CANCEL: 'cancel',
  DISMISS: 'dismiss',
  OPENED: 'opened',
  LOCKED: 'locked',
};

export const openBrowserAsync = async (url, options = {}) => {
  // On web, just open in new tab/window
  const target = options.showInRecents === false ? '_blank' : '_self';
  window.open(url, target);
  
  return {
    type: WebBrowserResultType.OPENED,
    url: url,
  };
};

export const openAuthSessionAsync = async (url, redirectUrl, options = {}) => {
  // For web, we'll simulate auth session
  return new Promise((resolve) => {
    const authWindow = window.open(url, '_blank', 'width=500,height=600');
    
    const checkClosed = setInterval(() => {
      if (authWindow.closed) {
        clearInterval(checkClosed);
        resolve({
          type: WebBrowserResultType.CANCEL,
          url: null,
        });
      }
    }, 1000);
    
    // Listen for redirect
    window.addEventListener('message', (event) => {
      if (event.origin === window.location.origin) {
        clearInterval(checkClosed);
        authWindow.close();
        resolve({
          type: WebBrowserResultType.OPENED,
          url: event.data.url || redirectUrl,
        });
      }
    });
  });
};

export const dismissBrowser = async () => {
  // On web, this is a no-op since we can't control external windows
  return;
};

export const warmUpAsync = async (browserPackage) => {
  // No-op on web
  return {};
};

export const mayInitWithUrlAsync = async (url, browserPackage) => {
  // No-op on web
  return {};
};

export const coolDownAsync = async (browserPackage) => {
  // No-op on web
  return {};
};

export const getCustomTabsSupportingBrowsersAsync = async () => {
  // Return empty array on web
  return [];
};

export const getDefaultUserAgent = () => {
  return navigator.userAgent;
};

// Default export
export default {
  WebBrowserResultType,
  openBrowserAsync,
  openAuthSessionAsync,
  dismissBrowser,
  warmUpAsync,
  mayInitWithUrlAsync,
  coolDownAsync,
  getCustomTabsSupportingBrowsersAsync,
  getDefaultUserAgent,
};
