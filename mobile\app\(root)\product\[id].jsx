import { useUser } from '@clerk/clerk-expo';
import { ScrollView, View, Alert, TouchableOpacity } from 'react-native';
import { useRouter, useLocalSearchParams } from 'expo-router';
import { useState, useEffect } from 'react';
import { useCart } from '../../../contexts/CartContext';
import { useWishlist } from '../../../contexts/WishlistContext';
import { Box } from "@/components/ui/box";
import { Heading } from "@/components/ui/heading";
import { Text } from "@/components/ui/text";
import { VStack } from "@/components/ui/vstack";
import { HStack } from "@/components/ui/hstack";
import { Image } from "@/components/ui/image";
import { Button, ButtonText } from "@/components/ui/button";
import { Ionicons } from '@expo/vector-icons';
import { COLORS } from '../../../constants/colors';

// Safe function to get product image
const getProductImage = (product) => {
  try {
    if (!product.images) {
      return 'https://via.placeholder.com/400x300/f0f0f0/999999?text=No+Image';
    }
    
    if (typeof product.images === 'string') {
      try {
        const parsedImages = JSON.parse(product.images);
        return Array.isArray(parsedImages) && parsedImages.length > 0 
          ? parsedImages[0] 
          : 'https://via.placeholder.com/400x300/f0f0f0/999999?text=No+Image';
      } catch (parseError) {
        return product.images.startsWith('http') 
          ? product.images 
          : 'https://via.placeholder.com/400x300/f0f0f0/999999?text=No+Image';
      }
    }
    
    if (Array.isArray(product.images) && product.images.length > 0) {
      return product.images[0];
    }
    
    return 'https://via.placeholder.com/400x300/f0f0f0/999999?text=No+Image';
  } catch (error) {
    console.warn('Error getting product image:', error);
    return 'https://via.placeholder.com/400x300/f0f0f0/999999?text=No+Image';
  }
};

export default function ProductDetailPage() {
  const { user } = useUser();
  const { addToCart, getCartItem } = useCart();
  const { toggleWishlist, isInWishlist } = useWishlist();
  const router = useRouter();
  const { id } = useLocalSearchParams();
  const [product, setProduct] = useState(null);
  const [loading, setLoading] = useState(true);
  const [quantity, setQuantity] = useState(1);

  useEffect(() => {
    fetchProduct();
  }, [id]);

  const fetchProduct = async () => {
    try {
      const response = await fetch(`http://localhost:5002/api/products/${id}`);
      if (response.ok) {
        const productData = await response.json();
        setProduct(productData);
      } else {
        Alert.alert('Hata', 'Ürün bulunamadı');
        router.back();
      }
    } catch (error) {
      console.error('Error fetching product:', error);
      Alert.alert('Hata', 'Ürün yüklenirken bir hata oluştu');
      router.back();
    } finally {
      setLoading(false);
    }
  };

  const handleAddToCart = () => {
    addToCart(product, quantity);
    Alert.alert('Sepete Eklendi', `${product.name} sepete eklendi`);
  };

  const handleBuyNow = () => {
    addToCart(product, quantity);
    router.push('/cart');
  };

  const handleWishlistToggle = () => {
    const isAdded = toggleWishlist(product);
    if (isAdded) {
      Alert.alert('Favorilere Eklendi', `${product.name} favorilere eklendi`);
    } else {
      Alert.alert('Favorilerden Kaldırıldı', `${product.name} favorilerden kaldırıldı`);
    }
  };

  const increaseQuantity = () => {
    setQuantity(prev => prev + 1);
  };

  const decreaseQuantity = () => {
    if (quantity > 1) {
      setQuantity(prev => prev - 1);
    }
  };

  if (loading) {
    return (
      <View style={styles.container}>
        <Box className="flex-1 justify-center items-center">
          <Text className="text-gray-500">Ürün yükleniyor...</Text>
        </Box>
      </View>
    );
  }

  if (!product) {
    return (
      <View style={styles.container}>
        <Box className="flex-1 justify-center items-center">
          <Text className="text-gray-500">Ürün bulunamadı</Text>
        </Box>
      </View>
    );
  }

  const discountPercentage = product.original_price > product.price
    ? Math.round(((product.original_price - product.price) / product.original_price) * 100)
    : 0;

  const cartItem = getCartItem(product.id);

  return (
    <View style={styles.container}>
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* Back Button */}
        <TouchableOpacity style={styles.backButton} onPress={() => router.back()}>
          <Ionicons name="arrow-back" size={24} color={COLORS.text} />
        </TouchableOpacity>

        {/* Product Image */}
        <Image
          source={{ uri: getProductImage(product) }}
          alt={product.name}
          style={styles.productImage}
        />

        {/* Product Info */}
        <Box className="p-6">
          <VStack className="space-y-4">
            {/* Product Name */}
            <Heading size="xl" className="text-gray-900">
              {product.name}
            </Heading>

            {/* Price Section */}
            <HStack className="items-center space-x-3">
              <Text className="text-2xl font-bold text-green-600">
                ₺{product.price?.toFixed(2)}
              </Text>
              {discountPercentage > 0 && (
                <>
                  <Text className="text-lg text-gray-500 line-through">
                    ₺{product.original_price?.toFixed(2)}
                  </Text>
                  <Box className="bg-red-500 px-2 py-1 rounded">
                    <Text className="text-white text-sm font-bold">
                      %{discountPercentage} İndirim
                    </Text>
                  </Box>
                </>
              )}
            </HStack>

            {/* Description */}
            <Box>
              <Heading size="md" className="mb-2 text-gray-900">
                Ürün Açıklaması
              </Heading>
              <Text className="text-gray-600 leading-6">
                {product.description || 'Bu ürün için açıklama bulunmamaktadır.'}
              </Text>
            </Box>

            {/* Stock Info */}
            <Box>
              <Text className={`font-semibold ${product.stock > 0 ? 'text-green-600' : 'text-red-600'}`}>
                {product.stock > 0 ? `Stokta ${product.stock} adet` : 'Stokta yok'}
              </Text>
            </Box>

            {/* Quantity Selector */}
            {product.stock > 0 && (
              <Box>
                <Heading size="md" className="mb-3 text-gray-900">
                  Miktar
                </Heading>
                <HStack className="items-center space-x-4">
                  <TouchableOpacity 
                    style={styles.quantityButton} 
                    onPress={decreaseQuantity}
                    disabled={quantity <= 1}
                  >
                    <Ionicons name="remove" size={20} color={quantity <= 1 ? COLORS.textLight : COLORS.text} />
                  </TouchableOpacity>
                  
                  <Text className="text-xl font-semibold min-w-[40px] text-center">
                    {quantity}
                  </Text>
                  
                  <TouchableOpacity 
                    style={styles.quantityButton} 
                    onPress={increaseQuantity}
                    disabled={quantity >= product.stock}
                  >
                    <Ionicons name="add" size={20} color={quantity >= product.stock ? COLORS.textLight : COLORS.text} />
                  </TouchableOpacity>
                </HStack>
              </Box>
            )}

            {/* Cart Status */}
            {cartItem && (
              <Box className="bg-blue-50 p-3 rounded-lg">
                <Text className="text-blue-700 font-semibold">
                  Sepetinizde {cartItem.quantity} adet var
                </Text>
              </Box>
            )}
          </VStack>
        </Box>
      </ScrollView>

      {/* Bottom Action Buttons */}
      {product.stock > 0 && (
        <Box className="p-4 bg-white border-t border-gray-200">
          <HStack className="space-x-3 mb-3">
            <Button
              variant="outline"
              className="flex-1"
              onPress={handleAddToCart}
            >
              <ButtonText>Sepete Ekle</ButtonText>
            </Button>

            <Button
              className="flex-1 bg-green-600"
              onPress={handleBuyNow}
            >
              <ButtonText>Hemen Al</ButtonText>
            </Button>
          </HStack>

          <Button
            variant="outline"
            className="w-full"
            onPress={handleWishlistToggle}
          >
            <HStack className="items-center space-x-2">
              <Ionicons
                name={isInWishlist(product.id) ? "heart" : "heart-outline"}
                size={20}
                color={isInWishlist(product.id) ? "#EF4444" : COLORS.text}
              />
              <ButtonText>
                {isInWishlist(product.id) ? "Favorilerden Kaldır" : "Favorilere Ekle"}
              </ButtonText>
            </HStack>
          </Button>
        </Box>
      )}
    </View>
  );
}

const styles = {
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  scrollView: {
    flex: 1,
  },
  backButton: {
    position: 'absolute',
    top: 50,
    left: 20,
    zIndex: 10,
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    borderRadius: 20,
    padding: 8,
  },
  productImage: {
    width: '100%',
    height: 400,
    resizeMode: 'cover',
  },
  quantityButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#f0f0f0',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
};
