import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { neon } from "@neondatabase/serverless";
import "dotenv/config";

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Database connection
const sql = neon(process.env.DATABASE_URL);

// Read products from JSON file
const productsPath = path.join(__dirname, '../../mobile/assets/product.json');
const productsData = JSON.parse(fs.readFileSync(productsPath, 'utf8'));

// Get or create categories
const getOrCreateCategory = async (categoryName) => {
  let category = await sql`SELECT * FROM categories WHERE name = ${categoryName}`;

  if (category.length === 0) {
    const result = await sql`INSERT INTO categories (name) VALUES (${categoryName}) RETURNING *`;
    category = result[0];
  } else {
    category = category[0];
  }

  return category;
};

// Get or create subcategories
const getOrCreateSubcategory = async (subcategoryName, categoryId) => {
  let subcategory = await sql`SELECT * FROM subcategories WHERE name = ${subcategoryName} AND category_id = ${categoryId}`;

  if (subcategory.length === 0) {
    const result = await sql`INSERT INTO subcategories (name, category_id) VALUES (${subcategoryName}, ${categoryId}) RETURNING *`;
    subcategory = result[0];
  } else {
    subcategory = subcategory[0];
  }

  return subcategory;
};

// Category mapping for products
const categoryMapping = {
  'AirPods Pro': { category: 'Elektronik', subcategory: 'Ses Sistemleri' },
  'Apple Watch Series 9': { category: 'Elektronik', subcategory: 'Akıllı Saatler' },
  'Bose Noise Cancelling Headphones': { category: 'Elektronik', subcategory: 'Ses Sistemleri' },
  'Dyson V15 Vacuum': { category: 'Ev & Yaşam', subcategory: 'Temizlik' },
  'Samsung Galaxy S24 Ultra': { category: 'Elektronik', subcategory: 'Telefonlar' },
  'iPad Pro': { category: 'Elektronik', subcategory: 'Tabletler' },
  'iPhone 16 Pro': { category: 'Elektronik', subcategory: 'Telefonlar' },
  'MacBook Pro 16-inch': { category: 'Elektronik', subcategory: 'Bilgisayarlar' },
  'Nike Air Max 270': { category: 'Spor & Outdoor', subcategory: 'Ayakkabı' },
  'PlayStation 5': { category: 'Elektronik', subcategory: 'Oyun Konsolları' }
};

async function importProducts() {
  console.log('Starting product import...');

  try {
  for (const product of productsData) {
    console.log(`Processing: ${product.name}`);

    // Get category mapping
    const mapping = categoryMapping[product.name] || { category: 'Genel', subcategory: 'Diğer' };

    // Get or create category
    const category = await getOrCreateCategory(mapping.category);

    // Get or create subcategory
    const subcategory = await getOrCreateSubcategory(mapping.subcategory, category.id);

    // Check if product already exists
    const existingProduct = await sql`SELECT * FROM products WHERE name = ${product.name}`;

    if (existingProduct.length === 0) {
      // Insert product
      const result = await sql`
        INSERT INTO products (
          name,
          description,
          price,
          original_price,
          discount_percentage,
          images,
          category,
          stock,
          featured,
          status,
          seller_id,
          specifications,
          tags,
          dynamic_pricing
        ) VALUES (
          ${product.name},
          ${product.description},
          ${product.price},
          ${product.price * 1.2},
          ${Math.round(((product.price * 1.2 - product.price) / (product.price * 1.2)) * 100)},
          ${JSON.stringify([product.image])},
          ${mapping.category},
          ${Math.floor(Math.random() * 100) + 10},
          ${Math.random() > 0.5},
          ${'active'},
          ${'system'},
          ${JSON.stringify({})},
          ${JSON.stringify([mapping.category, mapping.subcategory])},
          ${JSON.stringify({})}
        ) RETURNING *
      `;

      console.log(`✅ Added: ${product.name} (ID: ${result[0].id})`);
    } else {
      console.log(`⚠️  Already exists: ${product.name}`);
    }
  }

  console.log('\n🎉 Product import completed successfully!');

  // Show summary
  const totalProducts = await sql`SELECT COUNT(*) as count FROM products`;
  const totalCategories = await sql`SELECT COUNT(*) as count FROM categories`;
  const totalSubcategories = await sql`SELECT COUNT(*) as count FROM subcategories`;

  console.log(`\n📊 Database Summary:`);
  console.log(`   Products: ${totalProducts[0].count}`);
  console.log(`   Categories: ${totalCategories[0].count}`);
  console.log(`   Subcategories: ${totalSubcategories[0].count}`);

  } catch (error) {
    console.error('❌ Error importing products:', error);
  }
}

// Run the import
importProducts();
