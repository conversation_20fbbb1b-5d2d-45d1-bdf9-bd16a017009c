{"name": "jscodeshift", "version": "0.15.2", "description": "A toolkit for JavaScript codemods", "repository": {"type": "git", "url": "https://github.com/facebook/jscodeshift.git"}, "bugs": "https://github.com/facebook/jscodeshift/issues", "main": "index.js", "scripts": {"prepare": "cp -R src/ dist/", "test": "jest --bail", "docs": "rm -rf docs && jsdoc -d docs -R README.md src/collections/* src/core.js src/Collection.js"}, "bin": {"jscodeshift": "./bin/jscodeshift.js"}, "keywords": ["codemod", "recast", "babel"], "author": "<PERSON>", "license": "MIT", "dependencies": {"@babel/core": "^7.23.0", "@babel/parser": "^7.23.0", "@babel/plugin-transform-class-properties": "^7.22.5", "@babel/plugin-transform-modules-commonjs": "^7.23.0", "@babel/plugin-transform-nullish-coalescing-operator": "^7.22.11", "@babel/plugin-transform-optional-chaining": "^7.23.0", "@babel/plugin-transform-private-methods": "^7.22.5", "@babel/preset-flow": "^7.22.15", "@babel/preset-typescript": "^7.23.0", "@babel/register": "^7.22.15", "babel-core": "^7.0.0-bridge.0", "chalk": "^4.1.2", "flow-parser": "0.*", "graceful-fs": "^4.2.4", "micromatch": "^4.0.4", "neo-async": "^2.5.0", "node-dir": "^0.1.17", "recast": "^0.23.3", "temp": "^0.8.4", "write-file-atomic": "^2.3.0"}, "peerDependencies": {"@babel/preset-env": "^7.1.6"}, "peerDependenciesMeta": {"@babel/preset-env": {"optional": true}}, "devDependencies": {"babel-eslint": "^10.0.1", "eslint": "^5.9.0", "jest": "^29.3.1", "jsdoc": "3.6.7", "mkdirp": "^0.5.1"}, "jest": {"roots": ["src", "bin", "parser", "sample"]}}