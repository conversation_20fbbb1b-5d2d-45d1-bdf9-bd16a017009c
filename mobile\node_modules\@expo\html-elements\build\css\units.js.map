{"version": 3, "file": "units.js", "sourceRoot": "", "sources": ["../../src/css/units.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,UAAU,EAAE,QAAQ,EAAE,MAAM,cAAc,CAAC;AAEpD,MAAM,UAAU,GAAG,CAAC,KAAa;IAC/B,IAAI,QAAQ,CAAC,EAAE,KAAK,KAAK;QAAE,OAAO,GAAG,KAAK,KAAK,CAAC;IAChD,OAAO,UAAU,CAAC,YAAY,EAAE,GAAG,EAAE,GAAG,KAAK,CAAC;AAChD,CAAC;AAED,MAAM,UAAU,EAAE,CAAC,KAAa;IAC9B,IAAI,QAAQ,CAAC,EAAE,KAAK,KAAK;QAAE,OAAO,GAAG,KAAK,IAAI,CAAC;IAC/C,OAAO,GAAG,CAAC,KAAK,CAAC,CAAC;AACpB,CAAC", "sourcesContent": ["import { PixelRatio, Platform } from 'react-native';\n\nexport function rem(value: number): number | string {\n  if (Platform.OS === 'web') return `${value}rem`;\n  return PixelRatio.getFontScale() * 16 * value;\n}\n\nexport function em(value: number): number | string {\n  if (Platform.OS === 'web') return `${value}em`;\n  return rem(value);\n}\n"]}