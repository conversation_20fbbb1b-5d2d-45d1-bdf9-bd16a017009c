{"name": "@gluestack-ui/image", "version": "0.1.17", "main": "lib/index", "module": "lib/index", "types": "lib/index.d.ts", "react-native": "src/index", "source": "src/index", "typings": "lib/index.d.ts", "description": "A universal headless Button component for React Native, Next.js & React", "keywords": ["react", "native", "react-native", "button", "gluestack-ui", "universal", "headless", "typescript", "component", "android", "ios", "nextjs"], "scripts": {"prepare": "tsc", "release": "release-it", "watch": "tsc --watch", "build": "tsc", "clean": "rm -rf lib", "dev:web": "cd example/native && yarn web --clear", "storybook": "cd example/native/storybook && yarn web"}, "devDependencies": {"@types/react": "^18.0.22", "@types/react-native": "^0.72.3", "babel-plugin-transform-remove-console": "^6.9.4", "react": "^18.1.0", "react-dom": "^18.1.0", "react-native": "^0.72.4", "react-native-builder-bob": "^0.20.1", "react-native-web": "^0.19.9", "tsconfig": "7", "typescript": "^5.6.3"}, "dependencies": {"@gluestack-ui/utils": "^0.1.15", "@react-native-aria/focus": "^0.2.9", "@react-native-aria/interactions": "0.2.16"}, "peerDependencies": {"react": ">=16", "react-dom": ">=16"}, "homepage": "https://github.com/gluestack/gluestack-ui/tree/main/packages/unstyled/image#readme", "repository": {"type": "git", "url": "git+https://github.com/gluestack/gluestack-ui.git"}, "files": ["lib/", "src/"], "jest": {"preset": "jest-expo", "transform": {"^.+\\.js$": "<rootDir>/node_modules/react-native/jest/preprocessor.js"}, "modulePathIgnorePatterns": ["<rootDir>/example/*", "<rootDir>/lib/"], "transformIgnorePatterns": ["node_modules/(?!(@react-native|react-native|expo-asset|expo-constants|@unimodules|react-native-unimodules|expo-font|react-native-svg|@expo/vector-icons|react-native-vector-icons|@react-native-aria/checkbox|@react-native-aria/interactions|@react-native-aria/button|@react-native-aria/switch|@react-native-aria/toggle|@react-native-aria/utils|@react-native-aria/*))"], "setupFiles": ["<rootDir>/src/jest/mock.ts"]}}