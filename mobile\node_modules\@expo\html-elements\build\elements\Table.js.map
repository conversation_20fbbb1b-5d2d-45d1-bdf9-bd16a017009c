{"version": 3, "file": "Table.js", "sourceRoot": "", "sources": ["../../src/elements/Table.tsx"], "names": [], "mappings": "AAAA,OAAO,KAAK,EAAE,EAAiB,UAAU,EAAE,MAAM,OAAO,CAAC;AACzD,OAAO,EAAE,UAAU,EAAE,MAAM,cAAc,CAAC;AAE1C,OAAO,EAAE,EAAE,EAAE,MAAM,cAAc,CAAC;AAClC,OAAO,EAAE,SAAS,EAAkB,MAAM,qBAAqB,CAAC;AAChE,OAAO,IAAmB,MAAM,oBAAoB,CAAC;AACrD,OAAO,IAAmB,MAAM,oBAAoB,CAAC;AAErD,MAAM,CAAC,MAAM,KAAK,GAAG,UAAU,CAAC,CAAC,KAAgB,EAAE,GAAG,EAAE,EAAE;IACxD,OAAO,oBAAC,IAAI,OAAK,KAAK,EAAE,GAAG,EAAE,GAAG,GAAI,CAAC;AACvC,CAAC,CAA6B,CAAC;AAE/B,MAAM,CAAC,MAAM,KAAK,GAAG,UAAU,CAAC,CAAC,KAAgB,EAAE,GAAG,EAAE,EAAE;IACxD,OAAO,oBAAC,IAAI,OAAK,KAAK,EAAE,GAAG,EAAE,GAAG,GAAI,CAAC;AACvC,CAAC,CAA6B,CAAC;AAE/B,MAAM,CAAC,MAAM,KAAK,GAAG,UAAU,CAAC,CAAC,KAAgB,EAAE,GAAG,EAAE,EAAE;IACxD,OAAO,oBAAC,IAAI,OAAK,KAAK,EAAE,GAAG,EAAE,GAAG,GAAI,CAAC;AACvC,CAAC,CAA6B,CAAC;AAE/B,MAAM,CAAC,MAAM,KAAK,GAAG,UAAU,CAAC,CAAC,KAAgB,EAAE,GAAG,EAAE,EAAE;IACxD,OAAO,oBAAC,IAAI,OAAK,KAAK,EAAE,GAAG,EAAE,GAAG,GAAI,CAAC;AACvC,CAAC,CAA6B,CAAC;AAE/B,MAAM,CAAC,MAAM,EAAE,GAAG,UAAU,CAAC,CAAC,KAAqB,EAAE,GAAQ,EAAE,EAAE;IAC/D,OAAO,oBAAC,SAAS,OAAK,KAAK,EAAE,KAAK,EAAE,CAAC,MAAM,CAAC,EAAE,EAAE,KAAK,CAAC,KAAK,CAAC,EAAE,GAAG,EAAE,GAAG,GAAI,CAAC;AAC7E,CAAC,CAAkC,CAAC;AAEpC,MAAM,CAAC,MAAM,EAAE,GAAG,UAAU,CAAC,CAAC,KAAgB,EAAE,GAAG,EAAE,EAAE;IACrD,OAAO,oBAAC,IAAI,OAAK,KAAK,EAAE,KAAK,EAAE,CAAC,MAAM,CAAC,EAAE,EAAE,KAAK,CAAC,KAAK,CAAC,EAAE,GAAG,EAAE,GAAG,GAAI,CAAC;AACxE,CAAC,CAA6B,CAAC;AAE/B,MAAM,CAAC,MAAM,EAAE,GAAG,UAAU,CAAC,CAAC,KAAqB,EAAE,GAAQ,EAAE,EAAE;IAC/D,OAAO,oBAAC,SAAS,OAAK,KAAK,EAAE,KAAK,EAAE,CAAC,MAAM,CAAC,EAAE,EAAE,KAAK,CAAC,KAAK,CAAC,EAAE,GAAG,EAAE,GAAG,GAAI,CAAC;AAC7E,CAAC,CAAkC,CAAC;AAEpC,MAAM,CAAC,MAAM,OAAO,GAAG,UAAU,CAAC,CAAC,KAAgB,EAAE,GAAQ,EAAE,EAAE;IAC/D,OAAO,oBAAC,IAAI,OAAK,KAAK,EAAE,KAAK,EAAE,CAAC,MAAM,CAAC,OAAO,EAAE,KAAK,CAAC,KAAK,CAAC,EAAE,GAAG,EAAE,GAAG,GAAI,CAAC;AAC7E,CAAC,CAA6B,CAAC;AAE/B,MAAM,MAAM,GAAG,UAAU,CAAC,MAAM,CAAC;IAC/B,OAAO,EAAE;QACP,SAAS,EAAE,QAAQ;QACnB,QAAQ,EAAE,EAAE,CAAC,CAAC,CAAW;KAC1B;IACD,EAAE,EAAE;QACF,SAAS,EAAE,QAAQ;QACnB,UAAU,EAAE,MAAM;QAClB,IAAI,EAAE,CAAC;QACP,QAAQ,EAAE,EAAE,CAAC,CAAC,CAAW;KAC1B;IACD,EAAE,EAAE;QACF,aAAa,EAAE,KAAK;KACrB;IACD,EAAE,EAAE;QACF,IAAI,EAAE,CAAC;QACP,QAAQ,EAAE,EAAE,CAAC,CAAC,CAAW;KAC1B;CACF,CAAC,CAAC", "sourcesContent": ["import React, { ComponentType, forwardRef } from 'react';\nimport { StyleSheet } from 'react-native';\n\nimport { em } from '../css/units';\nimport { TableText, TableTextProps } from '../primitives/Table';\nimport Text, { TextProps } from '../primitives/Text';\nimport View, { ViewProps } from '../primitives/View';\n\nexport const Table = forwardRef((props: ViewProps, ref) => {\n  return <View {...props} ref={ref} />;\n}) as ComponentType<ViewProps>;\n\nexport const THead = forwardRef((props: ViewProps, ref) => {\n  return <View {...props} ref={ref} />;\n}) as ComponentType<ViewProps>;\n\nexport const TBody = forwardRef((props: ViewProps, ref) => {\n  return <View {...props} ref={ref} />;\n}) as ComponentType<ViewProps>;\n\nexport const TFoot = forwardRef((props: ViewProps, ref) => {\n  return <View {...props} ref={ref} />;\n}) as ComponentType<ViewProps>;\n\nexport const TH = forwardRef((props: TableTextProps, ref: any) => {\n  return <TableText {...props} style={[styles.th, props.style]} ref={ref} />;\n}) as ComponentType<TableTextProps>;\n\nexport const TR = forwardRef((props: ViewProps, ref) => {\n  return <View {...props} style={[styles.tr, props.style]} ref={ref} />;\n}) as ComponentType<ViewProps>;\n\nexport const TD = forwardRef((props: TableTextProps, ref: any) => {\n  return <TableText {...props} style={[styles.td, props.style]} ref={ref} />;\n}) as ComponentType<TableTextProps>;\n\nexport const Caption = forwardRef((props: TextProps, ref: any) => {\n  return <Text {...props} style={[styles.caption, props.style]} ref={ref} />;\n}) as ComponentType<TextProps>;\n\nconst styles = StyleSheet.create({\n  caption: {\n    textAlign: 'center',\n    fontSize: em(1) as number,\n  },\n  th: {\n    textAlign: 'center',\n    fontWeight: 'bold',\n    flex: 1,\n    fontSize: em(1) as number,\n  },\n  tr: {\n    flexDirection: 'row',\n  },\n  td: {\n    flex: 1,\n    fontSize: em(1) as number,\n  },\n});\n"]}