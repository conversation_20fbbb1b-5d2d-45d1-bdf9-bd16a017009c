{"version": 3, "file": "Text.js", "sourceRoot": "", "sources": ["../../src/elements/Text.tsx"], "names": [], "mappings": "AAAA,OAAO,KAAK,EAAE,EAAiB,UAAU,EAAE,MAAM,OAAO,CAAC;AACzD,OAAO,EAAE,UAAU,EAAE,QAAQ,EAAE,MAAM,cAAc,CAAC;AAEpD,OAAO,EAAE,EAAE,EAAE,MAAM,cAAc,CAAC;AAClC,OAAO,IAAmB,MAAM,oBAAoB,CAAC;AACrD,OAAO,IAAmB,MAAM,oBAAoB,CAAC;AAGrD,MAAM,CAAC,MAAM,CAAC,GAAG,UAAU,CAAC,CAAC,EAAE,KAAK,EAAE,GAAG,KAAK,EAAa,EAAE,GAAG,EAAE,EAAE;IAClE,OAAO,oBAAC,IAAI,OAAK,KAAK,EAAE,KAAK,EAAE,CAAC,MAAM,CAAC,CAAC,EAAE,KAAK,CAAC,EAAE,GAAG,EAAE,GAAG,GAAI,CAAC;AACjE,CAAC,CAA6B,CAAC;AAE/B,MAAM,CAAC,MAAM,CAAC,GAAG,UAAU,CAAC,CAAC,EAAE,KAAK,EAAE,GAAG,KAAK,EAAa,EAAE,GAAG,EAAE,EAAE;IAClE,OAAO,oBAAC,IAAI,OAAK,KAAK,EAAE,KAAK,EAAE,CAAC,MAAM,CAAC,CAAC,EAAE,KAAK,CAAC,EAAE,GAAG,EAAE,GAAG,GAAI,CAAC;AACjE,CAAC,CAA6B,CAAC;AAE/B,MAAM,CAAC,MAAM,CAAC,GAAG,UAAU,CAAC,CAAC,EAAE,KAAK,EAAE,GAAG,KAAK,EAAa,EAAE,GAAG,EAAE,EAAE;IAClE,OAAO,oBAAC,IAAI,OAAK,KAAK,EAAE,KAAK,EAAE,CAAC,MAAM,CAAC,CAAC,EAAE,KAAK,CAAC,EAAE,GAAG,EAAE,GAAG,GAAI,CAAC;AACjE,CAAC,CAA6B,CAAC;AAE/B,MAAM,CAAC,MAAM,CAAC,GAAG,UAAU,CAAC,CAAC,EAAE,KAAK,EAAE,GAAG,KAAK,EAAa,EAAE,GAAG,EAAE,EAAE;IAClE,OAAO,oBAAC,IAAI,OAAK,KAAK,EAAE,KAAK,EAAE,CAAC,MAAM,CAAC,CAAC,EAAE,KAAK,CAAC,EAAE,GAAG,EAAE,GAAG,GAAI,CAAC;AACjE,CAAC,CAA6B,CAAC;AAE/B,MAAM,CAAC,MAAM,CAAC,GAAG,UAAU,CAAC,CAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,KAAK,EAAc,EAAE,GAAG,EAAE,EAAE;IACnF,OAAO,CACL,oBAAC,IAAI,OAAK,KAAK,EAAE,KAAK,EAAE,CAAC,MAAM,CAAC,CAAC,EAAE,KAAK,CAAC,EAAE,GAAG,EAAE,GAAG;;QAC/C,QAAQ;aACL,CACR,CAAC;AACJ,CAAC,CAA8B,CAAC;AAEhC,MAAM,CAAC,MAAM,UAAU,GAAG,UAAU,CAAC,CAAC,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,KAAK,EAAmB,EAAE,GAAG,EAAE,EAAE;IACvF,OAAO,oBAAC,IAAI,OAAK,KAAK,EAAE,KAAK,EAAE,CAAC,MAAM,CAAC,UAAU,EAAE,KAAK,CAAC,EAAE,GAAG,EAAE,GAAG,GAAI,CAAC;AAC1E,CAAC,CAAmC,CAAC;AAErC,MAAM,CAAC,MAAM,EAAE,GAAG,UAAU,CAAC,CAAC,EAAE,KAAK,EAAE,GAAG,KAAK,EAAa,EAAE,GAAG,EAAE,EAAE;IACnE,OAAO,oBAAC,IAAI,OAAK,KAAK,EAAE,KAAK,EAAE,CAAC,MAAM,CAAC,EAAE,EAAE,KAAK,CAAC,EAAE,GAAG,EAAE,GAAG,GAAI,CAAC;AAClE,CAAC,CAA6B,CAAC;AAE/B,MAAM,CAAC,MAAM,IAAI,GAAG,UAAU,CAAC,CAAC,EAAE,KAAK,EAAE,GAAG,KAAK,EAAa,EAAE,GAAG,EAAE,EAAE;IACrE,OAAO,oBAAC,IAAI,OAAK,KAAK,EAAE,KAAK,EAAE,CAAC,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,GAAG,EAAE,GAAG,GAAI,CAAC;AACpE,CAAC,CAA6B,CAAC;AAE/B,MAAM,CAAC,MAAM,IAAI,GAAG,UAAU,CAAC,CAAC,EAAE,KAAK,EAAE,GAAG,KAAK,EAAa,EAAE,GAAG,EAAE,EAAE;IACrE,OAAO,oBAAC,IAAI,OAAK,KAAK,EAAE,KAAK,EAAE,CAAC,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,GAAG,EAAE,GAAG,GAAI,CAAC;AACpE,CAAC,CAA6B,CAAC;AAE/B,SAAS,WAAW,CAAC,KAAU;IAC7B,OAAO,OAAO,KAAK,CAAC,QAAQ,KAAK,QAAQ,CAAC;AAC5C,CAAC;AAID,MAAM,CAAC,MAAM,GAAG,GAAG,UAAU,CAAC,CAAC,KAAe,EAAE,GAAQ,EAAE,EAAE;IAC1D,IAAI,WAAW,CAAC,KAAK,CAAC,EAAE;QACtB,OAAO,oBAAC,IAAI,OAAK,KAAK,EAAE,KAAK,EAAE,CAAC,MAAM,CAAC,IAAI,EAAE,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,KAAK,CAAC,EAAE,GAAG,EAAE,GAAG,GAAI,CAAC;KACrF;IACD,OAAO,oBAAC,IAAI,OAAK,KAAK,EAAE,KAAK,EAAE,CAAC,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,KAAK,CAAC,EAAE,GAAG,EAAE,GAAG,GAAI,CAAC;AACzE,CAAC,CAA4B,CAAC;AAE9B,oEAAoE;AACpE,MAAM,CAAC,MAAM,IAAI,GAAG,UAAU,CAAC,CAAC,EAAE,QAAQ,EAAE,GAAG,KAAK,EAAa,EAAE,GAAG,EAAE,EAAE;IACxE,OAAO,oBAAC,IAAI,OAAK,KAAK,EAAE,GAAG,EAAE,GAAG,GAAI,CAAC;AACvC,CAAC,CAA6B,CAAC;AAE/B,MAAM,CAAC,MAAM,MAAM,GAAG,CAAC,CAAC;AACxB,MAAM,CAAC,MAAM,GAAG,GAAG,CAAC,CAAC;AACrB,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;AACpB,MAAM,CAAC,MAAM,IAAI,GAAG,IAAI,CAAC;AAEzB,MAAM,MAAM,GAAG,UAAU,CAAC,MAAM,CAAC;IAC/B,CAAC,EAAE;QACD,cAAc,EAAE,EAAE,CAAC,CAAC,CAAC;KACtB;IACD,CAAC,EAAE;QACD,UAAU,EAAE,MAAM;KACnB;IACD,CAAC,EAAE;QACD,SAAS,EAAE,QAAQ;KACpB;IACD,IAAI,EAAE;QACJ,UAAU,EAAE,QAAQ,CAAC,MAAM,CAAC,EAAE,OAAO,EAAE,SAAS,EAAE,GAAG,EAAE,aAAa,EAAE,OAAO,EAAE,WAAW,EAAE,CAAC;QAC7F,UAAU,EAAE,KAAK;KAClB;IACD,GAAG,EAAE;QACH,cAAc,EAAE,EAAE,CAAC,CAAC,CAAC;KACtB;IACD,UAAU,EAAE;QACV,cAAc,EAAE,EAAE,CAAC,CAAC,CAAC;KACtB;IACD,EAAE,EAAE;QACF,KAAK,EAAE,CAAC;QACR,MAAM,EAAE,EAAE,CAAC,GAAG,CAAC;KAChB;IACD,CAAC,EAAE;QACD,kBAAkB,EAAE,cAAc;QAClC,mBAAmB,EAAE,OAAO;KAC7B;IACD,IAAI,EAAE;QACJ,eAAe,EAAE,QAAQ;QACzB,KAAK,EAAE,OAAO;KACf;IACD,CAAC,EAAE;QACD,SAAS,EAAE,QAAQ;KACpB;CACF,CAAC,CAAC", "sourcesContent": ["import React, { ComponentType, forwardRef } from 'react';\nimport { StyleSheet, Platform } from 'react-native';\n\nimport { em } from '../css/units';\nimport Text, { TextProps } from '../primitives/Text';\nimport View, { ViewProps } from '../primitives/View';\nimport { BlockQuoteProps, QuoteProps, TimeProps } from './Text.types';\n\nexport const P = forwardRef(({ style, ...props }: TextProps, ref) => {\n  return <Text {...props} style={[styles.p, style]} ref={ref} />;\n}) as ComponentType<TextProps>;\n\nexport const B = forwardRef(({ style, ...props }: TextProps, ref) => {\n  return <Text {...props} style={[styles.b, style]} ref={ref} />;\n}) as ComponentType<TextProps>;\n\nexport const S = forwardRef(({ style, ...props }: TextProps, ref) => {\n  return <Text {...props} style={[styles.s, style]} ref={ref} />;\n}) as ComponentType<TextProps>;\n\nexport const I = forwardRef(({ style, ...props }: TextProps, ref) => {\n  return <Text {...props} style={[styles.i, style]} ref={ref} />;\n}) as ComponentType<TextProps>;\n\nexport const Q = forwardRef(({ children, cite, style, ...props }: QuoteProps, ref) => {\n  return (\n    <Text {...props} style={[styles.q, style]} ref={ref}>\n      \"{children}\"\n    </Text>\n  );\n}) as ComponentType<QuoteProps>;\n\nexport const BlockQuote = forwardRef(({ style, cite, ...props }: BlockQuoteProps, ref) => {\n  return <View {...props} style={[styles.blockQuote, style]} ref={ref} />;\n}) as ComponentType<BlockQuoteProps>;\n\nexport const BR = forwardRef(({ style, ...props }: TextProps, ref) => {\n  return <Text {...props} style={[styles.br, style]} ref={ref} />;\n}) as ComponentType<TextProps>;\n\nexport const Mark = forwardRef(({ style, ...props }: TextProps, ref) => {\n  return <Text {...props} style={[styles.mark, style]} ref={ref} />;\n}) as ComponentType<TextProps>;\n\nexport const Code = forwardRef(({ style, ...props }: TextProps, ref) => {\n  return <Text {...props} style={[styles.code, style]} ref={ref} />;\n}) as ComponentType<TextProps>;\n\nfunction isTextProps(props: any): props is TextProps {\n  return typeof props.children === 'string';\n}\n\ntype PreProps = TextProps | ViewProps;\n\nexport const Pre = forwardRef((props: PreProps, ref: any) => {\n  if (isTextProps(props)) {\n    return <Text {...props} style={[styles.code, styles.pre, props.style]} ref={ref} />;\n  }\n  return <View {...props} style={[styles.pre, props.style]} ref={ref} />;\n}) as ComponentType<PreProps>;\n\n// Extract dateTime to prevent passing it to the native Text element\nexport const Time = forwardRef(({ dateTime, ...props }: TimeProps, ref) => {\n  return <Text {...props} ref={ref} />;\n}) as ComponentType<TimeProps>;\n\nexport const Strong = B;\nexport const Del = S;\nexport const EM = I;\nexport const Span = Text;\n\nconst styles = StyleSheet.create({\n  p: {\n    marginVertical: em(1),\n  },\n  b: {\n    fontWeight: 'bold',\n  },\n  q: {\n    fontStyle: 'italic',\n  },\n  code: {\n    fontFamily: Platform.select({ default: 'Courier', ios: 'Courier New', android: 'monospace' }),\n    fontWeight: '500',\n  },\n  pre: {\n    marginVertical: em(1),\n  },\n  blockQuote: {\n    marginVertical: em(1),\n  },\n  br: {\n    width: 0,\n    height: em(0.5),\n  },\n  s: {\n    textDecorationLine: 'line-through',\n    textDecorationStyle: 'solid',\n  },\n  mark: {\n    backgroundColor: 'yellow',\n    color: 'black',\n  },\n  i: {\n    fontStyle: 'italic',\n  },\n});\n"]}