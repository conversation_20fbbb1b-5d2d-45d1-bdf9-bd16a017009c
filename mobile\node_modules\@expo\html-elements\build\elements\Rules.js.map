{"version": 3, "file": "Rules.js", "sourceRoot": "", "sources": ["../../src/elements/Rules.tsx"], "names": [], "mappings": "AAAA,OAAO,KAAK,EAAE,EAAiB,UAAU,EAAE,MAAM,OAAO,CAAC;AACzD,OAAO,EAAE,UAAU,EAAE,MAAM,cAAc,CAAC;AAE1C,OAAO,IAAmB,MAAM,oBAAoB,CAAC;AAErD,MAAM,CAAC,MAAM,EAAE,GAAG,UAAU,CAAC,CAAC,KAAgB,EAAE,GAAG,EAAE,EAAE;IACrD,OAAO,oBAAC,IAAI,OAAK,KAAK,EAAE,KAAK,EAAE,CAAC,MAAM,CAAC,EAAE,EAAE,KAAK,CAAC,KAAK,CAAC,EAAE,GAAG,EAAE,GAAG,GAAI,CAAC;AACxE,CAAC,CAA6B,CAAC;AAE/B,MAAM,MAAM,GAAG,UAAU,CAAC,MAAM,CAAC;IAC/B,EAAE,EAAE;QACF,cAAc,EAAE,UAAU,CAAC,aAAa;QACxC,iBAAiB,EAAE,UAAU,CAAC,aAAa;QAC3C,cAAc,EAAE,SAAS;QACzB,iBAAiB,EAAE,SAAS;QAC5B,cAAc,EAAE,CAAC;KAClB;CACF,CAAC,CAAC", "sourcesContent": ["import React, { ComponentType, forwardRef } from 'react';\nimport { StyleSheet } from 'react-native';\n\nimport View, { ViewProps } from '../primitives/View';\n\nexport const HR = forwardRef((props: ViewProps, ref) => {\n  return <View {...props} style={[styles.hr, props.style]} ref={ref} />;\n}) as ComponentType<ViewProps>;\n\nconst styles = StyleSheet.create({\n  hr: {\n    borderTopWidth: StyleSheet.hairlineWidth,\n    borderBottomWidth: StyleSheet.hairlineWidth,\n    borderTopColor: '#9A9A9A',\n    borderBottomColor: '#EEEEEE',\n    marginVertical: 8,\n  },\n});\n"]}