
import React from 'react'
import { View, Text, TouchableOpacity } from 'react-native'
import {styles} from '../assets/styles/home.styles'
import { COLORS } from '../constants/colors'
import { Ionicons } from '@expo/vector-icons'
import { useTransactions } from '../hooks/useTransactions'
import { formatDate } from '../lib/utils'

const CATEGORIES = 
[
  { id: 1, name:"Food & Drinks", icon:"fast-food"},
  { id: 2, name:"Shopping", icon:"cart"},
  { id: 3, name: "Income", icon: "cash" },
  { id: 4, name: "Expense", icon: "cash" },
  { id: 5, name: "Transfer", icon: "swap-horizontal" },
  { id: 6, name: "Entertainment", icon: "game-controller" },
  { id: 7, name: "Transportation", icon: "car" },
  { id: 8, name: "Health", icon: "medkit" },
  { id: 9, name: "Education", icon: "book" },
  { id: 10, name: "Housing", icon: "home" },
  ];

export const Categories = ({ selectedCategory, setSelectedCategory }) => {
  return (
    <View style={styles.categoryGrid}>
      {CATEGORIES.map((category) => (
        <TouchableOpacity
          key={category.id}
          className="flex-1 items-center justify-center flex-wrap gap-4"
          style={[styles.categoryButton, selectedCategory === category.name ? styles.categoryButtonActive : null]}
          onPress={() => setSelectedCategory(category.name)}
        >
                <Ionicons name={category.icon} size={24} color={selectedCategory === category.name ? COLORS.white : COLORS.text} />
                <Text style={[styles.categoryButtonText, selectedCategory === category.name ? styles.categoryButtonTextActive : null]}>
                  {category.name}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        );
      };
      


