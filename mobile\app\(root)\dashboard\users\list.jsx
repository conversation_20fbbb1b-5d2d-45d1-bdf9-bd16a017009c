import React, { useState, useEffect } from 'react';
import { View, Text, FlatList, TouchableOpacity, Alert } from 'react-native';
import { useUser } from '@clerk/clerk-expo';
import { useRouter } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import { COLORS } from '@/constants/colors';
import { API_URL } from '@/constants/api';
import { Navbar } from '../../../../components/Navbar';
import { styles } from '../../../../assets/styles/dashboard.styles';

export default function UsersList() {
  const { user } = useUser();
  const router = useRouter();
  const [searchQuery, setSearchQuery] = useState('');
  const [users, setUsers] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchUsers();
  }, []);

  const fetchUsers = async () => {
    try {
      setLoading(true);
      const response = await fetch(`${API_URL}/api/users`);
      const data = await response.json();
      setUsers(data);
    } catch (error) {
      console.error('Error fetching users:', error);
      Alert.alert('Hata', 'Kullanıcılar yüklenirken bir hata oluştu');
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteUser = async (userId) => {
    Alert.alert(
      'Kullanıcıyı Sil',
      'Bu kullanıcıyı silmek istediğinizden emin misiniz?',
      [
        { text: 'İptal', style: 'cancel' },
        {
          text: 'Sil',
          style: 'destructive',
          onPress: async () => {
            try {
              const response = await fetch(`${API_URL}/api/users/${userId}`, {
                method: 'DELETE',
              });
              
              if (response.ok) {
                Alert.alert('Başarılı', 'Kullanıcı başarıyla silindi');
                fetchUsers(); // Listeyi yenile
              } else {
                const errorData = await response.json();
                Alert.alert('Hata', errorData.message || 'Kullanıcı silinirken bir hata oluştu');
              }
            } catch (error) {
              console.error('Error deleting user:', error);
              Alert.alert('Hata', 'Kullanıcı silinirken bir hata oluştu');
            }
          }
        }
      ]
    );
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'active': return '#27ae60';
      case 'inactive': return '#e74c3c';
      case 'suspended': return '#f39c12';
      default: return COLORS.textLight;
    }
  };

  const getRoleColor = (role) => {
    switch (role) {
      case 'admin': return '#e74c3c';
      case 'moderator': return '#f39c12';
      case 'user': return '#3498db';
      default: return COLORS.textLight;
    }
  };

  const renderUserItem = ({ item }) => (
    <View style={styles.listItem}>
      <View style={styles.listItemContent}>
        <Text style={styles.listItemTitle}>{item.name}</Text>
        <Text style={styles.listItemSubtitle}>{item.email}</Text>
        <Text style={styles.listItemSubtitle}>@{item.username}</Text>
        
        <View style={styles.userBadges}>
          <View style={[styles.badge, { backgroundColor: getRoleColor(item.role) }]}>
            <Text style={styles.badgeText}>{item.role.toUpperCase()}</Text>
          </View>
          <View style={[styles.badge, { backgroundColor: getStatusColor(item.status) }]}>
            <Text style={styles.badgeText}>{item.status.toUpperCase()}</Text>
          </View>
          {item.is_email_verified && (
            <View style={[styles.badge, { backgroundColor: '#27ae60' }]}>
              <Text style={styles.badgeText}>VERİFİED</Text>
            </View>
          )}
        </View>
        
        <Text style={styles.listItemDescription}>
          Kayıt: {new Date(item.created_at).toLocaleDateString('tr-TR')}
        </Text>
      </View>
      
      <View style={styles.listItemActions}>
        <TouchableOpacity
          style={[styles.actionButton, styles.editButton]}
          onPress={() => router.push(`/dashboard/users/edit/${item.id}`)}
        >
          <Ionicons name="pencil" size={16} color={COLORS.white} />
        </TouchableOpacity>
        
        <TouchableOpacity
          style={[styles.actionButton, styles.deleteButton]}
          onPress={() => handleDeleteUser(item.id)}
        >
          <Ionicons name="trash" size={16} color={COLORS.white} />
        </TouchableOpacity>
      </View>
    </View>
  );

  const filteredUsers = users.filter(user =>
    user.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    user.email.toLowerCase().includes(searchQuery.toLowerCase()) ||
    user.username.toLowerCase().includes(searchQuery.toLowerCase())
  );

  return (
    <View style={styles.container}>
      <Navbar 
        user={user} 
        searchQuery={searchQuery} 
        setSearchQuery={setSearchQuery}
        router={router}
      />
      
      <View style={styles.pageContent}>
        <View style={styles.pageHeader}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => router.back()}
          >
            <Ionicons name="arrow-back" size={24} color={COLORS.text} />
          </TouchableOpacity>
          
          <Text style={styles.pageTitle}>Kullanıcılar Listesi</Text>
          
          <TouchableOpacity
            style={styles.addButton}
            onPress={() => router.push('/dashboard/users/add')}
          >
            <Ionicons name="add" size={24} color={COLORS.white} />
            <Text style={styles.addButtonText}>Yeni Kullanıcı</Text>
          </TouchableOpacity>
        </View>

        {loading ? (
          <View style={styles.loadingContainer}>
            <Text style={styles.loadingText}>Yükleniyor...</Text>
          </View>
        ) : (
          <FlatList
            data={filteredUsers}
            renderItem={renderUserItem}
            keyExtractor={(item) => item.id.toString()}
            style={styles.list}
            contentContainerStyle={styles.listContent}
            showsVerticalScrollIndicator={false}
            ListEmptyComponent={
              <View style={styles.emptyContainer}>
                <Ionicons name="people-outline" size={64} color={COLORS.textLight} />
                <Text style={styles.emptyText}>Henüz kullanıcı bulunmuyor</Text>
                <TouchableOpacity
                  style={styles.emptyButton}
                  onPress={() => router.push('/dashboard/users/add')}
                >
                  <Text style={styles.emptyButtonText}>İlk Kullanıcıyı Ekle</Text>
                </TouchableOpacity>
              </View>
            }
          />
        )}
      </View>
    </View>
  );
}
