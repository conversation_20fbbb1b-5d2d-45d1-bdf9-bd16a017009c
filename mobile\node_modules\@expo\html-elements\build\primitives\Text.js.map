{"version": 3, "file": "Text.js", "sourceRoot": "", "sources": ["../../src/primitives/Text.tsx"], "names": [], "mappings": "AACA,OAAO,EAGL,IAAI,IAAI,UAAU,GAEnB,MAAM,cAAc,CAAC;AAEtB,OAAO,EAAE,oBAAoB,EAAE,MAAM,6BAA6B,CAAC;AA0DnE,MAAM,IAAI,GAAG,UAAsC,CAAC;AAEpD,eAAe,oBAAoB,CAAC,IAAI,CAA6B,CAAC", "sourcesContent": ["import { ClassAttributes, ComponentProps, ComponentType } from 'react';\nimport {\n  AccessibilityRole,\n  StyleProp,\n  Text as NativeText,\n  TextStyle as NativeTextStyle,\n} from 'react-native';\n\nimport { createSafeStyledView } from '../css/createSafeStyledView';\nimport { WebViewStyle } from './View';\n\n// https://github.com/necolas/react-native-web/issues/832\n\ntype NativeTextProps = ComponentProps<typeof NativeText> & ClassAttributes<typeof NativeText>;\n\nexport interface WebTextStyle {\n  /** string is only available on web */\n  fontSize?: NativeTextStyle['fontSize'] | string;\n  /** string is only available on web */\n  lineHeight?: NativeTextStyle['lineHeight'] | string;\n  /** @platform web */\n  fontFeatureSettings?: string;\n  /** @platform web */\n  textIndent?: string;\n  /** @platform web */\n  textOverflow?: string;\n  /** @platform web */\n  textRendering?: string;\n  /** @platform web */\n  textTransform?: string;\n  /** @platform web */\n  unicodeBidi?: string;\n  /** @platform web */\n  wordWrap?: string;\n}\n\nexport type TextStyle = Omit<NativeTextStyle, 'position' | 'fontSize' | 'lineHeight'> &\n  WebTextStyle &\n  WebViewStyle;\n\nexport type WebTextProps = {\n  style?: StyleProp<TextStyle>;\n  /** @platform web */\n  tabIndex?: number;\n  /** @platform web */\n  accessibilityLevel?: number;\n  accessibilityRole?: 'listitem' | 'heading' | AccessibilityRole;\n  /** @platform web */\n  href?: string;\n  /** @deprecated use the prop `hrefAttrs={{ target: '...' }}` instead. */\n  target?: string;\n  /** @platform web */\n  hrefAttrs?: {\n    /** @platform web */\n    target?: string;\n    /** @platform web */\n    rel?: string;\n    /** @platform web */\n    download?: boolean | string;\n  };\n  /** @platform web */\n  lang?: string;\n};\n\nexport type TextProps = Omit<NativeTextProps, 'style' | 'accessibilityRole'> & WebTextProps;\n\nconst Text = NativeText as ComponentType<TextProps>;\n\nexport default createSafeStyledView(Text) as ComponentType<TextProps>;\n"]}