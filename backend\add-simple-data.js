import { sql } from './config/db.js';

async function addSimpleData() {
    try {
        console.log('Adding simple sample data...');

        // 1. Categories
        const categories = [
            { name: 'Electronics' },
            { name: 'Fashion' },
            { name: 'Home & Garden' },
            { name: 'Sports' },
            { name: 'Books' },
            { name: 'Automotive' }
        ];

        console.log('Adding categories...');
        for (const category of categories) {
            try {
                await sql`
                    INSERT INTO categories (name) 
                    VALUES (${category.name})
                `;
            } catch (error) {
                // Ignore duplicate errors
                if (!error.message.includes('duplicate')) {
                    throw error;
                }
            }
        }

        // 2. More Products
        const moreProducts = [
            {
                name: 'Dell XPS 13 Laptop',
                description: 'Ultra-portable laptop with Intel i7 processor',
                price: 1199.99,
                original_price: 1299.99,
                discount_percentage: 7.69,
                images: ['https://example.com/dell-xps13.jpg'],
                category: 'Electronics',
                stock: 15,
                featured: true,
                status: 'active',
                seller_id: 'user_2example123',
                specifications: { processor: 'Intel i7', ram: '16GB', storage: '512GB SSD' },
                tags: ['laptop', 'dell', 'ultrabook'],
                dynamic_pricing: {}
            },
            {
                name: 'Adidas Running Shoes',
                description: 'Comfortable running shoes for daily training',
                price: 89.99,
                original_price: 109.99,
                discount_percentage: 18.18,
                images: ['https://example.com/adidas-running.jpg'],
                category: 'Sports',
                stock: 50,
                featured: false,
                status: 'active',
                seller_id: 'user_2example123',
                specifications: { size: '42', color: 'Blue/White', material: 'Mesh' },
                tags: ['shoes', 'adidas', 'running', 'sports'],
                dynamic_pricing: {}
            },
            {
                name: 'Wireless Bluetooth Speaker',
                description: 'Portable speaker with excellent sound quality',
                price: 49.99,
                original_price: 69.99,
                discount_percentage: 28.57,
                images: ['https://example.com/bluetooth-speaker.jpg'],
                category: 'Electronics',
                stock: 80,
                featured: true,
                status: 'active',
                seller_id: 'user_2example123',
                specifications: { battery: '12 hours', connectivity: 'Bluetooth 5.0' },
                tags: ['speaker', 'bluetooth', 'portable'],
                dynamic_pricing: {}
            },
            {
                name: 'Gaming Mechanical Keyboard',
                description: 'RGB backlit mechanical keyboard for gaming',
                price: 129.99,
                original_price: 159.99,
                discount_percentage: 18.75,
                images: ['https://example.com/gaming-keyboard.jpg'],
                category: 'Electronics',
                stock: 25,
                featured: false,
                status: 'active',
                seller_id: 'user_2example123',
                specifications: { switches: 'Cherry MX Blue', backlight: 'RGB' },
                tags: ['keyboard', 'gaming', 'mechanical'],
                dynamic_pricing: {}
            },
            {
                name: 'Yoga Mat Premium',
                description: 'High-quality yoga mat for all types of yoga practice',
                price: 39.99,
                original_price: 49.99,
                discount_percentage: 20,
                images: ['https://example.com/yoga-mat.jpg'],
                category: 'Sports',
                stock: 100,
                featured: true,
                status: 'active',
                seller_id: 'user_2example123',
                specifications: { thickness: '6mm', material: 'TPE', size: '183x61cm' },
                tags: ['yoga', 'fitness', 'mat'],
                dynamic_pricing: {}
            }
        ];

        console.log('Adding more products...');
        for (const product of moreProducts) {
            try {
                await sql`
                    INSERT INTO products (
                        name, description, price, original_price, discount_percentage,
                        images, category, stock, featured, status, seller_id,
                        specifications, tags, dynamic_pricing
                    ) VALUES (
                        ${product.name}, ${product.description}, ${product.price}, 
                        ${product.original_price}, ${product.discount_percentage},
                        ${JSON.stringify(product.images)}, ${product.category}, 
                        ${product.stock}, ${product.featured}, ${product.status}, 
                        ${product.seller_id}, ${JSON.stringify(product.specifications)}, 
                        ${JSON.stringify(product.tags)}, ${JSON.stringify(product.dynamic_pricing)}
                    )
                `;
            } catch (error) {
                console.log(`Error adding product ${product.name}:`, error.message);
            }
        }

        console.log('✅ Simple sample data added successfully!');
        console.log('📊 Added:');
        console.log('- 6 Categories');
        console.log('- 5 Additional Products');
        
        process.exit(0);
    } catch (error) {
        console.error('❌ Error adding sample data:', error);
        process.exit(1);
    }
}

addSimpleData();
