{"name": "mobile", "main": "expo-router/entry", "version": "1.0.0", "scripts": {"start": "expo start", "reset-project": "node ./scripts/reset-project.js", "android": "DARK_MODE=media expo start --android", "ios": "DARK_MODE=media expo start --ios", "web": "DARK_MODE=media expo start --web", "lint": "expo lint"}, "dependencies": {"@clerk/clerk-expo": "^2.12.0", "@clerk/clerk-react": "^5.31.8", "@clerk/nextjs": "^6.20.2", "@expo/html-elements": "^0.4.2", "@expo/vector-icons": "^14.1.0", "@gluestack-ui/button": "^1.0.14", "@gluestack-ui/icon": "^0.1.27", "@gluestack-ui/image": "^0.1.17", "@gluestack-ui/nativewind-utils": "^1.0.26", "@gluestack-ui/overlay": "^0.1.22", "@gluestack-ui/toast": "^1.0.9", "@react-navigation/bottom-tabs": "^7.3.10", "@react-navigation/elements": "^2.3.8", "@react-navigation/native": "^7.1.6", "babel-plugin-module-resolver": "^5.0.2", "expo": "~53.0.9", "expo-auth-session": "~6.1.5", "expo-blur": "~14.1.4", "expo-constants": "~17.1.6", "expo-font": "~13.3.1", "expo-haptics": "~14.1.4", "expo-image": "~2.1.7", "expo-linking": "~7.1.5", "expo-router": "~5.0.6", "expo-secure-store": "^14.2.3", "expo-splash-screen": "~0.30.8", "expo-status-bar": "~2.2.3", "expo-symbols": "~0.4.4", "expo-system-ui": "~5.0.7", "expo-web-browser": "~14.1.6", "nativewind": "^4.1.23", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "^0.79.2", "react-native-css-interop": "^0.1.22", "react-native-gesture-handler": "~2.24.0", "react-native-keyboard-aware-scroll-view": "^0.9.5", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "^5.4.1", "react-native-screens": "~4.10.0", "react-native-svg": "^15.2.0", "react-native-web": "~0.20.0", "react-native-webview": "13.13.5", "tailwindcss": "^3.4.17"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/react": "~19.0.10", "eslint": "^9.25.0", "eslint-config-expo": "~9.2.0", "jscodeshift": "^0.15.2", "typescript": "~5.8.3"}, "private": true}