{"version": 3, "file": "_Text.web.js", "sourceRoot": "", "sources": ["../../src/elements/_Text.web.tsx"], "names": [], "mappings": "AAAA,OAAO,EAAiB,UAAU,EAAE,MAAM,OAAO,CAAC;AAClD,OAAO,EAAE,UAAU,EAAE,MAAM,cAAc,CAAC;AAC1C,OAAO,aAAa,MAAM,6CAA6C,CAAC;AAKxE,MAAM,CAAC,MAAM,CAAC,GAAG,UAAU,CAAC,CAAC,EAAE,KAAK,EAAE,GAAG,KAAK,EAAa,EAAE,GAAG,EAAE,EAAE;IAClE,OAAO,aAAa,CAAC,GAAG,EAAE,EAAE,GAAG,KAAK,EAAE,KAAK,EAAE,CAAC,MAAM,CAAC,KAAK,EAAE,KAAK,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;AAC7E,CAAC,CAA6B,CAAC;AAE/B,MAAM,CAAC,MAAM,CAAC,GAAG,UAAU,CAAC,CAAC,EAAE,KAAK,EAAE,GAAG,KAAK,EAAa,EAAE,GAAG,EAAE,EAAE;IAClE,OAAO,aAAa,CAAC,GAAG,EAAE,EAAE,GAAG,KAAK,EAAE,KAAK,EAAE,CAAC,MAAM,CAAC,KAAK,EAAE,KAAK,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;AAC7E,CAAC,CAA6B,CAAC;AAE/B,MAAM,CAAC,MAAM,CAAC,GAAG,UAAU,CAAC,CAAC,EAAE,KAAK,EAAE,GAAG,KAAK,EAAa,EAAE,GAAG,EAAE,EAAE;IAClE,OAAO,aAAa,CAAC,GAAG,EAAE,EAAE,GAAG,KAAK,EAAE,KAAK,EAAE,CAAC,MAAM,CAAC,KAAK,EAAE,KAAK,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;AAC7E,CAAC,CAA6B,CAAC;AAE/B,MAAM,CAAC,MAAM,GAAG,GAAG,UAAU,CAAC,CAAC,EAAE,KAAK,EAAE,GAAG,KAAK,EAAa,EAAE,GAAG,EAAE,EAAE;IACpE,OAAO,aAAa,CAAC,KAAK,EAAE,EAAE,GAAG,KAAK,EAAE,KAAK,EAAE,CAAC,MAAM,CAAC,KAAK,EAAE,KAAK,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;AAC/E,CAAC,CAA6B,CAAC;AAE/B,MAAM,CAAC,MAAM,MAAM,GAAG,UAAU,CAAC,CAAC,EAAE,KAAK,EAAE,GAAG,KAAK,EAAa,EAAE,GAAG,EAAE,EAAE;IACvE,OAAO,aAAa,CAAC,QAAQ,EAAE,EAAE,GAAG,KAAK,EAAE,KAAK,EAAE,CAAC,MAAM,CAAC,KAAK,EAAE,KAAK,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;AAClF,CAAC,CAA6B,CAAC;AAE/B,MAAM,CAAC,MAAM,CAAC,GAAG,UAAU,CAAC,CAAC,EAAE,KAAK,EAAE,GAAG,KAAK,EAAa,EAAE,GAAG,EAAE,EAAE;IAClE,OAAO,aAAa,CAAC,GAAG,EAAE,EAAE,GAAG,KAAK,EAAE,KAAK,EAAE,CAAC,MAAM,CAAC,KAAK,EAAE,KAAK,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;AAC7E,CAAC,CAA6B,CAAC;AAE/B,MAAM,CAAC,MAAM,CAAC,GAAG,UAAU,CAAC,CAAC,EAAE,KAAK,EAAE,GAAG,KAAK,EAAc,EAAE,GAAG,EAAE,EAAE;IACnE,OAAO,aAAa,CAAC,GAAG,EAAE,EAAE,GAAG,KAAK,EAAE,KAAK,EAAE,CAAC,MAAM,CAAC,KAAK,EAAE,KAAK,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;AAC7E,CAAC,CAA8B,CAAC;AAEhC,MAAM,CAAC,MAAM,UAAU,GAAG,UAAU,CAAC,CAAC,EAAE,KAAK,EAAE,GAAG,KAAK,EAAmB,EAAE,GAAG,EAAE,EAAE;IACjF,OAAO,aAAa,CAAC,YAAY,EAAE,EAAE,GAAG,KAAK,EAAE,KAAK,EAAE,CAAC,MAAM,CAAC,KAAK,EAAE,KAAK,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;AACtF,CAAC,CAAmC,CAAC;AAErC,MAAM,CAAC,MAAM,EAAE,GAAG,UAAU,CAAC,CAAC,EAAE,KAAK,EAAE,GAAG,KAAK,EAAa,EAAE,GAAG,EAAE,EAAE;IACnE,OAAO,aAAa,CAAC,IAAI,EAAE,EAAE,GAAG,KAAK,EAAE,KAAK,EAAE,CAAC,MAAM,CAAC,KAAK,EAAE,KAAK,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;AAC9E,CAAC,CAA6B,CAAC;AAE/B,MAAM,CAAC,MAAM,EAAE,GAAG,UAAU,CAAC,CAAC,KAAgB,EAAE,GAAG,EAAE,EAAE;IACrD,OAAO,aAAa,CAAC,IAAI,EAAE,EAAE,GAAG,KAAK,EAAE,GAAG,EAAE,CAAC,CAAC;AAChD,CAAC,CAA6B,CAAC;AAE/B,MAAM,CAAC,MAAM,KAAK,GAAG,UAAU,CAAC,CAAC,EAAE,KAAK,EAAE,GAAG,KAAK,EAAa,EAAE,GAAG,EAAE,EAAE;IACtE,OAAO,aAAa,CAAC,OAAO,EAAE,EAAE,GAAG,KAAK,EAAE,KAAK,EAAE,CAAC,MAAM,CAAC,KAAK,EAAE,KAAK,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;AACjF,CAAC,CAA6B,CAAC;AAE/B,MAAM,CAAC,MAAM,IAAI,GAAG,UAAU,CAAC,CAAC,EAAE,KAAK,EAAE,GAAG,KAAK,EAAa,EAAE,GAAG,EAAE,EAAE;IACrE,OAAO,aAAa,CAAC,MAAM,EAAE,EAAE,GAAG,KAAK,EAAE,KAAK,EAAE,CAAC,MAAM,CAAC,KAAK,EAAE,KAAK,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;AAChF,CAAC,CAA6B,CAAC;AAE/B,MAAM,CAAC,MAAM,IAAI,GAAG,UAAU,CAAC,CAAC,KAAgB,EAAE,GAAG,EAAE,EAAE;IACvD,OAAO,aAAa,CAAC,MAAM,EAAE,EAAE,GAAG,KAAK,EAAE,GAAG,EAAE,CAAC,CAAC;AAClD,CAAC,CAA6B,CAAC;AAE/B,MAAM,CAAC,MAAM,IAAI,GAAG,UAAU,CAAC,CAAC,EAAE,KAAK,EAAE,GAAG,KAAK,EAAa,EAAE,GAAG,EAAE,EAAE;IACrE,OAAO,aAAa,CAAC,MAAM,EAAE,EAAE,GAAG,KAAK,EAAE,KAAK,EAAE,CAAC,MAAM,CAAC,KAAK,EAAE,KAAK,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;AAChF,CAAC,CAA6B,CAAC;AAE/B,MAAM,CAAC,MAAM,GAAG,GAAG,UAAU,CAAC,CAAC,EAAE,KAAK,EAAE,GAAG,KAAK,EAAa,EAAE,GAAG,EAAE,EAAE;IACpE,OAAO,aAAa,CAAC,KAAK,EAAE,EAAE,GAAG,KAAK,EAAE,KAAK,EAAE,CAAC,MAAM,CAAC,KAAK,EAAE,KAAK,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;AAC/E,CAAC,CAA6B,CAAC;AAE/B,MAAM,MAAM,GAAG,UAAU,CAAC,MAAM,CAAC;IAC/B,KAAK,EAAE;QACL,UAAU,EAAE,QAAQ;QACpB,KAAK,EAAE,MAAM;QACb,MAAM,EAAE,eAAe;QACvB,SAAS,EAAE,YAAY;QACvB,sCAAsC;QACtC,OAAO,EAAE,QAAQ;QACjB,MAAM,EAAE,CAAC;QACT,OAAO,EAAE,CAAC;QACV,UAAU,EAAE,UAAU;QACtB,QAAQ,EAAE,YAAY;KACvB;CACF,CAAC,CAAC", "sourcesContent": ["import { ComponentType, forwardRef } from 'react';\nimport { StyleSheet } from 'react-native';\nimport createElement from 'react-native-web/dist/exports/createElement';\n\nimport { TextProps } from '../primitives/Text';\nimport { BlockQuoteProps, QuoteProps, TimeProps } from './Text.types';\n\nexport const P = forwardRef(({ style, ...props }: TextProps, ref) => {\n  return createElement('p', { ...props, style: [styles.reset, style], ref });\n}) as ComponentType<TextProps>;\n\nexport const B = forwardRef(({ style, ...props }: TextProps, ref) => {\n  return createElement('b', { ...props, style: [styles.reset, style], ref });\n}) as ComponentType<TextProps>;\n\nexport const S = forwardRef(({ style, ...props }: TextProps, ref) => {\n  return createElement('s', { ...props, style: [styles.reset, style], ref });\n}) as ComponentType<TextProps>;\n\nexport const Del = forwardRef(({ style, ...props }: TextProps, ref) => {\n  return createElement('del', { ...props, style: [styles.reset, style], ref });\n}) as ComponentType<TextProps>;\n\nexport const Strong = forwardRef(({ style, ...props }: TextProps, ref) => {\n  return createElement('strong', { ...props, style: [styles.reset, style], ref });\n}) as ComponentType<TextProps>;\n\nexport const I = forwardRef(({ style, ...props }: TextProps, ref) => {\n  return createElement('i', { ...props, style: [styles.reset, style], ref });\n}) as ComponentType<TextProps>;\n\nexport const Q = forwardRef(({ style, ...props }: QuoteProps, ref) => {\n  return createElement('q', { ...props, style: [styles.reset, style], ref });\n}) as ComponentType<QuoteProps>;\n\nexport const BlockQuote = forwardRef(({ style, ...props }: BlockQuoteProps, ref) => {\n  return createElement('blockquote', { ...props, style: [styles.reset, style], ref });\n}) as ComponentType<BlockQuoteProps>;\n\nexport const EM = forwardRef(({ style, ...props }: TextProps, ref) => {\n  return createElement('em', { ...props, style: [styles.reset, style], ref });\n}) as ComponentType<TextProps>;\n\nexport const BR = forwardRef((props: TextProps, ref) => {\n  return createElement('br', { ...props, ref });\n}) as ComponentType<TextProps>;\n\nexport const Small = forwardRef(({ style, ...props }: TextProps, ref) => {\n  return createElement('small', { ...props, style: [styles.reset, style], ref });\n}) as ComponentType<TextProps>;\n\nexport const Mark = forwardRef(({ style, ...props }: TextProps, ref) => {\n  return createElement('mark', { ...props, style: [styles.reset, style], ref });\n}) as ComponentType<TextProps>;\n\nexport const Code = forwardRef((props: TextProps, ref) => {\n  return createElement('code', { ...props, ref });\n}) as ComponentType<TextProps>;\n\nexport const Time = forwardRef(({ style, ...props }: TimeProps, ref) => {\n  return createElement('time', { ...props, style: [styles.reset, style], ref });\n}) as ComponentType<TimeProps>;\n\nexport const Pre = forwardRef(({ style, ...props }: TextProps, ref) => {\n  return createElement('pre', { ...props, style: [styles.reset, style], ref });\n}) as ComponentType<TextProps>;\n\nconst styles = StyleSheet.create({\n  reset: {\n    fontFamily: 'System',\n    color: '#000',\n    border: '0 solid black',\n    boxSizing: 'border-box',\n    // @ts-ignore: inline is not supported\n    display: 'inline',\n    margin: 0,\n    padding: 0,\n    whiteSpace: 'pre-wrap',\n    wordWrap: 'break-word',\n  },\n});\n"]}