{"version": 3, "file": "Image.js", "sourceRoot": "", "sources": ["../../src/primitives/Image.tsx"], "names": [], "mappings": "AACA,OAAO,EAAE,KAAK,IAAI,WAAW,EAA6C,MAAM,cAAc,CAAC;AAE/F,OAAO,EAAE,oBAAoB,EAAE,MAAM,6BAA6B,CAAC;AAwBnE,MAAM,KAAK,GAAG,WAAwC,CAAC;AAEvD,eAAe,oBAAoB,CAAC,KAAK,CAA8B,CAAC", "sourcesContent": ["import { ClassAttributes, ComponentProps, ComponentType } from 'react';\nimport { Image as NativeImage, ImageStyle as NativeImageStyle, StyleProp } from 'react-native';\n\nimport { createSafeStyledView } from '../css/createSafeStyledView';\nimport { WebViewStyle } from './View';\n\ntype NativeImageProps = ComponentProps<typeof NativeImage> & ClassAttributes<typeof NativeImage>;\n\nexport interface WebImageStyle {\n  opacity?: number;\n}\n\nexport type ImageStyle = Omit<NativeImageStyle, 'position'> & WebImageStyle & WebViewStyle;\n\nexport type WebImageProps = {\n  style?: StyleProp<ImageStyle>;\n  /** @platform web */\n  tabIndex?: number;\n  /**\n   * Set whether the image can be dragged with native browser behavior.\n   * @platform web\n   */\n  draggable?: boolean;\n};\n\nexport type ImageProps = Omit<NativeImageProps, 'style'> & WebImageProps;\n\nconst Image = NativeImage as ComponentType<ImageProps>;\n\nexport default createSafeStyledView(Image) as ComponentType<ImageProps>;\n"]}