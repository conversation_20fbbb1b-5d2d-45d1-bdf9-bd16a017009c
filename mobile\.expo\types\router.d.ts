/* eslint-disable */
import * as Router from 'expo-router';

export * from 'expo-router';

declare module 'expo-router' {
  export namespace ExpoRouter {
    export interface __routes<T extends string | object = string> {
      hrefInputParams: { pathname: Router.RelativePathString, params?: Router.UnknownInputParams } | { pathname: Router.ExternalPathString, params?: Router.UnknownInputParams } | { pathname: `/_sitemap`; params?: Router.UnknownInputParams; } | { pathname: `${'/(auth)'}/sign-in` | `/sign-in`; params?: Router.UnknownInputParams; } | { pathname: `${'/(auth)'}/sign-up` | `/sign-up`; params?: Router.UnknownInputParams; } | { pathname: `${'/(root)'}/create` | `/create`; params?: Router.UnknownInputParams; } | { pathname: `${'/(root)'}/dashboard` | `/dashboard`; params?: Router.UnknownInputParams; } | { pathname: `${'/(root)'}` | `/`; params?: Router.UnknownInputParams; } | { pathname: `${'/(root)'}/ProductCreate` | `/ProductCreate`; params?: Router.UnknownInputParams; } | { pathname: `${'/(root)'}/dashboard/categories/list` | `/dashboard/categories/list`; params?: Router.UnknownInputParams; } | { pathname: `${'/(root)'}/dashboard/products/list` | `/dashboard/products/list`; params?: Router.UnknownInputParams; } | { pathname: `${'/(root)'}/dashboard/users/list` | `/dashboard/users/list`; params?: Router.UnknownInputParams; };
      hrefOutputParams: { pathname: Router.RelativePathString, params?: Router.UnknownOutputParams } | { pathname: Router.ExternalPathString, params?: Router.UnknownOutputParams } | { pathname: `/_sitemap`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(auth)'}/sign-in` | `/sign-in`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(auth)'}/sign-up` | `/sign-up`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(root)'}/create` | `/create`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(root)'}/dashboard` | `/dashboard`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(root)'}` | `/`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(root)'}/ProductCreate` | `/ProductCreate`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(root)'}/dashboard/categories/list` | `/dashboard/categories/list`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(root)'}/dashboard/products/list` | `/dashboard/products/list`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(root)'}/dashboard/users/list` | `/dashboard/users/list`; params?: Router.UnknownOutputParams; };
      href: Router.RelativePathString | Router.ExternalPathString | `/_sitemap${`?${string}` | `#${string}` | ''}` | `${'/(auth)'}/sign-in${`?${string}` | `#${string}` | ''}` | `/sign-in${`?${string}` | `#${string}` | ''}` | `${'/(auth)'}/sign-up${`?${string}` | `#${string}` | ''}` | `/sign-up${`?${string}` | `#${string}` | ''}` | `${'/(root)'}/create${`?${string}` | `#${string}` | ''}` | `/create${`?${string}` | `#${string}` | ''}` | `${'/(root)'}/dashboard${`?${string}` | `#${string}` | ''}` | `/dashboard${`?${string}` | `#${string}` | ''}` | `${'/(root)'}${`?${string}` | `#${string}` | ''}` | `/${`?${string}` | `#${string}` | ''}` | `${'/(root)'}/ProductCreate${`?${string}` | `#${string}` | ''}` | `/ProductCreate${`?${string}` | `#${string}` | ''}` | `${'/(root)'}/dashboard/categories/list${`?${string}` | `#${string}` | ''}` | `/dashboard/categories/list${`?${string}` | `#${string}` | ''}` | `${'/(root)'}/dashboard/products/list${`?${string}` | `#${string}` | ''}` | `/dashboard/products/list${`?${string}` | `#${string}` | ''}` | `${'/(root)'}/dashboard/users/list${`?${string}` | `#${string}` | ''}` | `/dashboard/users/list${`?${string}` | `#${string}` | ''}` | { pathname: Router.RelativePathString, params?: Router.UnknownInputParams } | { pathname: Router.ExternalPathString, params?: Router.UnknownInputParams } | { pathname: `/_sitemap`; params?: Router.UnknownInputParams; } | { pathname: `${'/(auth)'}/sign-in` | `/sign-in`; params?: Router.UnknownInputParams; } | { pathname: `${'/(auth)'}/sign-up` | `/sign-up`; params?: Router.UnknownInputParams; } | { pathname: `${'/(root)'}/create` | `/create`; params?: Router.UnknownInputParams; } | { pathname: `${'/(root)'}/dashboard` | `/dashboard`; params?: Router.UnknownInputParams; } | { pathname: `${'/(root)'}` | `/`; params?: Router.UnknownInputParams; } | { pathname: `${'/(root)'}/ProductCreate` | `/ProductCreate`; params?: Router.UnknownInputParams; } | { pathname: `${'/(root)'}/dashboard/categories/list` | `/dashboard/categories/list`; params?: Router.UnknownInputParams; } | { pathname: `${'/(root)'}/dashboard/products/list` | `/dashboard/products/list`; params?: Router.UnknownInputParams; } | { pathname: `${'/(root)'}/dashboard/users/list` | `/dashboard/users/list`; params?: Router.UnknownInputParams; };
    }
  }
}
