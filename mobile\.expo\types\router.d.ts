/* eslint-disable */
import * as Router from 'expo-router';

export * from 'expo-router';

declare module 'expo-router' {
  export namespace ExpoRouter {
    export interface __routes<T extends string | object = string> {
      hrefInputParams: { pathname: Router.RelativePathString, params?: Router.UnknownInputParams } | { pathname: Router.ExternalPathString, params?: Router.UnknownInputParams } | { pathname: `/_sitemap`; params?: Router.UnknownInputParams; } | { pathname: `${'/(auth)'}/sign-in` | `/sign-in`; params?: Router.UnknownInputParams; } | { pathname: `${'/(auth)'}/sign-up` | `/sign-up`; params?: Router.UnknownInputParams; } | { pathname: `${'/(root)'}/cart` | `/cart`; params?: Router.UnknownInputParams; } | { pathname: `${'/(root)'}/create` | `/create`; params?: Router.UnknownInputParams; } | { pathname: `${'/(root)'}/dashboard` | `/dashboard`; params?: Router.UnknownInputParams; } | { pathname: `${'/(root)'}` | `/`; params?: Router.UnknownInputParams; } | { pathname: `${'/(root)'}/sidebarItems` | `/sidebarItems`; params?: Router.UnknownInputParams; } | { pathname: `${'/(root)'}/dashboard/categories/add` | `/dashboard/categories/add`; params?: Router.UnknownInputParams; } | { pathname: `${'/(root)'}/dashboard/categories/list` | `/dashboard/categories/list`; params?: Router.UnknownInputParams; } | { pathname: `${'/(root)'}/dashboard/products/add` | `/dashboard/products/add`; params?: Router.UnknownInputParams; } | { pathname: `${'/(root)'}/dashboard/products/list` | `/dashboard/products/list`; params?: Router.UnknownInputParams; } | { pathname: `${'/(root)'}/dashboard/users/add` | `/dashboard/users/add`; params?: Router.UnknownInputParams; } | { pathname: `${'/(root)'}/dashboard/users/list` | `/dashboard/users/list`; params?: Router.UnknownInputParams; } | { pathname: `${'/(root)'}/dashboard/categories/edit/[id]` | `/dashboard/categories/edit/[id]`, params: Router.UnknownInputParams & { id: string | number; } } | { pathname: `${'/(root)'}/dashboard/products/edit/[id]` | `/dashboard/products/edit/[id]`, params: Router.UnknownInputParams & { id: string | number; } } | { pathname: `${'/(root)'}/dashboard/users/edit/[id]` | `/dashboard/users/edit/[id]`, params: Router.UnknownInputParams & { id: string | number; } } | { pathname: `${'/(root)'}/product/[id]` | `/product/[id]`, params: Router.UnknownInputParams & { id: string | number; } };
      hrefOutputParams: { pathname: Router.RelativePathString, params?: Router.UnknownOutputParams } | { pathname: Router.ExternalPathString, params?: Router.UnknownOutputParams } | { pathname: `/_sitemap`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(auth)'}/sign-in` | `/sign-in`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(auth)'}/sign-up` | `/sign-up`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(root)'}/cart` | `/cart`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(root)'}/create` | `/create`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(root)'}/dashboard` | `/dashboard`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(root)'}` | `/`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(root)'}/sidebarItems` | `/sidebarItems`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(root)'}/dashboard/categories/add` | `/dashboard/categories/add`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(root)'}/dashboard/categories/list` | `/dashboard/categories/list`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(root)'}/dashboard/products/add` | `/dashboard/products/add`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(root)'}/dashboard/products/list` | `/dashboard/products/list`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(root)'}/dashboard/users/add` | `/dashboard/users/add`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(root)'}/dashboard/users/list` | `/dashboard/users/list`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(root)'}/dashboard/categories/edit/[id]` | `/dashboard/categories/edit/[id]`, params: Router.UnknownOutputParams & { id: string; } } | { pathname: `${'/(root)'}/dashboard/products/edit/[id]` | `/dashboard/products/edit/[id]`, params: Router.UnknownOutputParams & { id: string; } } | { pathname: `${'/(root)'}/dashboard/users/edit/[id]` | `/dashboard/users/edit/[id]`, params: Router.UnknownOutputParams & { id: string; } } | { pathname: `${'/(root)'}/product/[id]` | `/product/[id]`, params: Router.UnknownOutputParams & { id: string; } };
      href: Router.RelativePathString | Router.ExternalPathString | `/_sitemap${`?${string}` | `#${string}` | ''}` | `${'/(auth)'}/sign-in${`?${string}` | `#${string}` | ''}` | `/sign-in${`?${string}` | `#${string}` | ''}` | `${'/(auth)'}/sign-up${`?${string}` | `#${string}` | ''}` | `/sign-up${`?${string}` | `#${string}` | ''}` | `${'/(root)'}/cart${`?${string}` | `#${string}` | ''}` | `/cart${`?${string}` | `#${string}` | ''}` | `${'/(root)'}/create${`?${string}` | `#${string}` | ''}` | `/create${`?${string}` | `#${string}` | ''}` | `${'/(root)'}/dashboard${`?${string}` | `#${string}` | ''}` | `/dashboard${`?${string}` | `#${string}` | ''}` | `${'/(root)'}${`?${string}` | `#${string}` | ''}` | `/${`?${string}` | `#${string}` | ''}` | `${'/(root)'}/sidebarItems${`?${string}` | `#${string}` | ''}` | `/sidebarItems${`?${string}` | `#${string}` | ''}` | `${'/(root)'}/dashboard/categories/add${`?${string}` | `#${string}` | ''}` | `/dashboard/categories/add${`?${string}` | `#${string}` | ''}` | `${'/(root)'}/dashboard/categories/list${`?${string}` | `#${string}` | ''}` | `/dashboard/categories/list${`?${string}` | `#${string}` | ''}` | `${'/(root)'}/dashboard/products/add${`?${string}` | `#${string}` | ''}` | `/dashboard/products/add${`?${string}` | `#${string}` | ''}` | `${'/(root)'}/dashboard/products/list${`?${string}` | `#${string}` | ''}` | `/dashboard/products/list${`?${string}` | `#${string}` | ''}` | `${'/(root)'}/dashboard/users/add${`?${string}` | `#${string}` | ''}` | `/dashboard/users/add${`?${string}` | `#${string}` | ''}` | `${'/(root)'}/dashboard/users/list${`?${string}` | `#${string}` | ''}` | `/dashboard/users/list${`?${string}` | `#${string}` | ''}` | { pathname: Router.RelativePathString, params?: Router.UnknownInputParams } | { pathname: Router.ExternalPathString, params?: Router.UnknownInputParams } | { pathname: `/_sitemap`; params?: Router.UnknownInputParams; } | { pathname: `${'/(auth)'}/sign-in` | `/sign-in`; params?: Router.UnknownInputParams; } | { pathname: `${'/(auth)'}/sign-up` | `/sign-up`; params?: Router.UnknownInputParams; } | { pathname: `${'/(root)'}/cart` | `/cart`; params?: Router.UnknownInputParams; } | { pathname: `${'/(root)'}/create` | `/create`; params?: Router.UnknownInputParams; } | { pathname: `${'/(root)'}/dashboard` | `/dashboard`; params?: Router.UnknownInputParams; } | { pathname: `${'/(root)'}` | `/`; params?: Router.UnknownInputParams; } | { pathname: `${'/(root)'}/sidebarItems` | `/sidebarItems`; params?: Router.UnknownInputParams; } | { pathname: `${'/(root)'}/dashboard/categories/add` | `/dashboard/categories/add`; params?: Router.UnknownInputParams; } | { pathname: `${'/(root)'}/dashboard/categories/list` | `/dashboard/categories/list`; params?: Router.UnknownInputParams; } | { pathname: `${'/(root)'}/dashboard/products/add` | `/dashboard/products/add`; params?: Router.UnknownInputParams; } | { pathname: `${'/(root)'}/dashboard/products/list` | `/dashboard/products/list`; params?: Router.UnknownInputParams; } | { pathname: `${'/(root)'}/dashboard/users/add` | `/dashboard/users/add`; params?: Router.UnknownInputParams; } | { pathname: `${'/(root)'}/dashboard/users/list` | `/dashboard/users/list`; params?: Router.UnknownInputParams; } | `${'/(root)'}/dashboard/categories/edit/${Router.SingleRoutePart<T>}` | `/dashboard/categories/edit/${Router.SingleRoutePart<T>}` | `${'/(root)'}/dashboard/products/edit/${Router.SingleRoutePart<T>}` | `/dashboard/products/edit/${Router.SingleRoutePart<T>}` | `${'/(root)'}/dashboard/users/edit/${Router.SingleRoutePart<T>}` | `/dashboard/users/edit/${Router.SingleRoutePart<T>}` | `${'/(root)'}/product/${Router.SingleRoutePart<T>}` | `/product/${Router.SingleRoutePart<T>}` | { pathname: `${'/(root)'}/dashboard/categories/edit/[id]` | `/dashboard/categories/edit/[id]`, params: Router.UnknownInputParams & { id: string | number; } } | { pathname: `${'/(root)'}/dashboard/products/edit/[id]` | `/dashboard/products/edit/[id]`, params: Router.UnknownInputParams & { id: string | number; } } | { pathname: `${'/(root)'}/dashboard/users/edit/[id]` | `/dashboard/users/edit/[id]`, params: Router.UnknownInputParams & { id: string | number; } } | { pathname: `${'/(root)'}/product/[id]` | `/product/[id]`, params: Router.UnknownInputParams & { id: string | number; } };
    }
  }
}
