import React, { useState, useEffect } from 'react';
import { View, Text, TextInput, TouchableOpacity, ScrollView, Alert } from 'react-native';
import { useUser } from '@clerk/clerk-expo';
import { useRouter, useLocalSearchParams } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import { COLORS } from '@/constants/colors';
import { API_URL } from '@/constants/api';
import { Navbar } from '../../../../../components/Navbar';
import { styles } from '../../../../../assets/styles/form.styles';

export default function EditUser() {
  const { user } = useUser();
  const router = useRouter();
  const { id } = useLocalSearchParams();
  const [searchQuery, setSearchQuery] = useState('');
  const [loading, setLoading] = useState(false);
  const [userLoading, setUserLoading] = useState(true);

  const [formData, setFormData] = useState({
    name: '',
    email: '',
    username: '',
    role: 'user',
    status: 'active',
    is_email_verified: false,
    can_bid: false,
    can_participate_in_lottery: false,
  });

  useEffect(() => {
    if (id) {
      fetchUser();
    }
  }, [id]);

  const fetchUser = async () => {
    try {
      setUserLoading(true);
      const response = await fetch(`${API_URL}/users/${id}`);
      
      if (response.ok) {
        const userData = await response.json();
        setFormData({
          name: userData.name || '',
          email: userData.email || '',
          username: userData.username || '',
          role: userData.role || 'user',
          status: userData.status || 'active',
          is_email_verified: userData.is_email_verified || false,
          can_bid: userData.can_bid || false,
          can_participate_in_lottery: userData.can_participate_in_lottery || false,
        });
      } else {
        Alert.alert('Hata', 'Kullanıcı bilgileri yüklenemedi');
        router.back();
      }
    } catch (error) {
      console.error('Error fetching user:', error);
      Alert.alert('Hata', 'Kullanıcı bilgileri yüklenirken bir hata oluştu');
      router.back();
    } finally {
      setUserLoading(false);
    }
  };

  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSubmit = async () => {
    try {
      // Validation
      if (!formData.name || !formData.email || !formData.username) {
        Alert.alert('Hata', 'Lütfen tüm zorunlu alanları doldurun');
        return;
      }

      // Email validation
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(formData.email)) {
        Alert.alert('Hata', 'Geçerli bir email adresi girin');
        return;
      }

      setLoading(true);

      const response = await fetch(`${API_URL}/users/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      if (response.ok) {
        Alert.alert('Başarılı', 'Kullanıcı başarıyla güncellendi', [
          { text: 'Tamam', onPress: () => router.back() }
        ]);
      } else {
        const errorData = await response.json();
        Alert.alert('Hata', errorData.message || 'Kullanıcı güncellenirken bir hata oluştu');
      }
    } catch (error) {
      console.error('Error updating user:', error);
      Alert.alert('Hata', 'Kullanıcı güncellenirken bir hata oluştu');
    } finally {
      setLoading(false);
    }
  };

  const roles = [
    { value: 'user', label: 'Kullanıcı' },
    { value: 'moderator', label: 'Moderatör' },
    { value: 'admin', label: 'Admin' }
  ];

  const statuses = [
    { value: 'active', label: 'Aktif' },
    { value: 'inactive', label: 'Pasif' },
    { value: 'suspended', label: 'Askıya Alınmış' }
  ];

  if (userLoading) {
    return (
      <View style={styles.container}>
        <Navbar 
          user={user} 
          searchQuery={searchQuery} 
          setSearchQuery={setSearchQuery}
          router={router}
        />
        <View style={styles.loadingContainer}>
          <Text style={styles.loadingText}>Kullanıcı bilgileri yükleniyor...</Text>
        </View>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <Navbar 
        user={user} 
        searchQuery={searchQuery} 
        setSearchQuery={setSearchQuery}
        router={router}
      />
      
      <ScrollView style={styles.formContainer}>
        <View style={styles.header}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => router.back()}
          >
            <Ionicons name="arrow-back" size={24} color={COLORS.text} />
          </TouchableOpacity>
          <Text style={styles.title}>Kullanıcı Düzenle</Text>
        </View>

        <View style={styles.form}>
          <View style={styles.inputGroup}>
            <Text style={styles.label}>Ad Soyad *</Text>
            <TextInput
              style={styles.input}
              value={formData.name}
              onChangeText={(value) => handleInputChange('name', value)}
              placeholder="Ad soyad girin"
              placeholderTextColor={COLORS.textLight}
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.label}>Email *</Text>
            <TextInput
              style={styles.input}
              value={formData.email}
              onChangeText={(value) => handleInputChange('email', value)}
              placeholder="<EMAIL>"
              placeholderTextColor={COLORS.textLight}
              keyboardType="email-address"
              autoCapitalize="none"
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.label}>Kullanıcı Adı *</Text>
            <TextInput
              style={styles.input}
              value={formData.username}
              onChangeText={(value) => handleInputChange('username', value)}
              placeholder="kullaniciadi"
              placeholderTextColor={COLORS.textLight}
              autoCapitalize="none"
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.label}>Rol</Text>
            <View style={styles.pickerContainer}>
              {roles.map((role) => (
                <TouchableOpacity
                  key={role.value}
                  style={[
                    styles.categoryOption,
                    formData.role === role.value && styles.categoryOptionSelected
                  ]}
                  onPress={() => handleInputChange('role', role.value)}
                >
                  <Text style={[
                    styles.categoryOptionText,
                    formData.role === role.value && styles.categoryOptionTextSelected
                  ]}>
                    {role.label}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.label}>Durum</Text>
            <View style={styles.pickerContainer}>
              {statuses.map((status) => (
                <TouchableOpacity
                  key={status.value}
                  style={[
                    styles.categoryOption,
                    formData.status === status.value && styles.categoryOptionSelected
                  ]}
                  onPress={() => handleInputChange('status', status.value)}
                >
                  <Text style={[
                    styles.categoryOptionText,
                    formData.status === status.value && styles.categoryOptionTextSelected
                  ]}>
                    {status.label}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>

          <View style={styles.checkboxGroup}>
            <TouchableOpacity
              style={styles.checkbox}
              onPress={() => handleInputChange('is_email_verified', !formData.is_email_verified)}
            >
              <Ionicons 
                name={formData.is_email_verified ? "checkbox" : "square-outline"} 
                size={24} 
                color={COLORS.primary} 
              />
              <Text style={styles.checkboxLabel}>Email doğrulanmış</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.checkbox}
              onPress={() => handleInputChange('can_bid', !formData.can_bid)}
            >
              <Ionicons 
                name={formData.can_bid ? "checkbox" : "square-outline"} 
                size={24} 
                color={COLORS.primary} 
              />
              <Text style={styles.checkboxLabel}>Teklif verebilir</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.checkbox}
              onPress={() => handleInputChange('can_participate_in_lottery', !formData.can_participate_in_lottery)}
            >
              <Ionicons 
                name={formData.can_participate_in_lottery ? "checkbox" : "square-outline"} 
                size={24} 
                color={COLORS.primary} 
              />
              <Text style={styles.checkboxLabel}>Çekilişe katılabilir</Text>
            </TouchableOpacity>
          </View>

          <TouchableOpacity
            style={[styles.submitButton, loading && styles.submitButtonDisabled]}
            onPress={handleSubmit}
            disabled={loading}
          >
            <Text style={styles.submitButtonText}>
              {loading ? 'Güncelleniyor...' : 'Kullanıcı Güncelle'}
            </Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </View>
  );
}
