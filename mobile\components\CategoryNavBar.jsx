import React, { useState } from 'react';
import { ScrollView, TouchableOpacity, View, Text } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { COLORS } from '../constants/colors';
import { useCategories } from '../hooks/useCategories';
import { useSubcategories } from '../hooks/useSubcategories';

export const CategoryNavBar = ({ onCategorySelect, onSubcategorySelect }) => {
  const { categories } = useCategories();
  const { getSubcategoriesByCategory } = useSubcategories();
  const [selectedCategory, setSelectedCategory] = useState(null);
  const [showSubcategories, setShowSubcategories] = useState(false);

  const handleCategoryPress = (category) => {
    if (selectedCategory?.id === category.id) {
      // Same category clicked - toggle subcategories
      setShowSubcategories(!showSubcategories);
    } else {
      // New category selected
      setSelectedCategory(category);
      setShowSubcategories(true);
      onCategorySelect && onCategorySelect(category);
    }
  };

  const handleSubcategoryPress = (subcategory) => {
    onSubcategorySelect && onSubcategorySelect(subcategory);
    setShowSubcategories(false);
  };

  const subcategories = selectedCategory ? getSubcategoriesByCategory(selectedCategory.id) : [];

  return (
    <View style={styles.container}>
      {/* Main Categories */}
      <ScrollView 
        horizontal 
        showsHorizontalScrollIndicator={false}
        style={styles.categoriesContainer}
        contentContainerStyle={styles.categoriesContent}
      >
        {categories.map((category) => (
          <TouchableOpacity
            key={category.id}
            style={[
              styles.categoryItem,
              selectedCategory?.id === category.id && styles.categoryItemSelected
            ]}
            onPress={() => handleCategoryPress(category)}
          >
            <Text style={[
              styles.categoryText,
              selectedCategory?.id === category.id && styles.categoryTextSelected
            ]}>
              {category.name}
            </Text>
            {getSubcategoriesByCategory(category.id).length > 0 && (
              <Ionicons 
                name={selectedCategory?.id === category.id && showSubcategories ? "chevron-up" : "chevron-down"} 
                size={16} 
                color={selectedCategory?.id === category.id ? COLORS.white : COLORS.textLight}
                style={styles.chevron}
              />
            )}
          </TouchableOpacity>
        ))}
      </ScrollView>

      {/* Subcategories */}
      {showSubcategories && subcategories.length > 0 && (
        <View style={styles.subcategoriesContainer}>
          <ScrollView 
            horizontal 
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.subcategoriesContent}
          >
            {subcategories.map((subcategory) => (
              <TouchableOpacity
                key={subcategory.id}
                style={styles.subcategoryItem}
                onPress={() => handleSubcategoryPress(subcategory)}
              >
                <Text style={styles.subcategoryText}>
                  {subcategory.name}
                </Text>
              </TouchableOpacity>
            ))}
          </ScrollView>
        </View>
      )}
    </View>
  );
};

const styles = {
  container: {
    backgroundColor: COLORS.card,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.border,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 3,
  },
  
  categoriesContainer: {
    paddingVertical: 15,
  },
  
  categoriesContent: {
    paddingHorizontal: 10,
    gap: 10,
  },
  
  categoryItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    backgroundColor: COLORS.background,
    borderWidth: 1,
    borderColor: COLORS.border,
    marginRight: 10,
  },
  
  categoryItemSelected: {
    backgroundColor: COLORS.primary,
    borderColor: COLORS.primary,
  },
  
  categoryText: {
    fontSize: 14,
    fontWeight: '500',
    color: COLORS.text,
  },
  
  categoryTextSelected: {
    color: COLORS.white,
  },
  
  chevron: {
    marginLeft: 5,
  },
  
  subcategoriesContainer: {
    backgroundColor: COLORS.background,
    paddingVertical: 10,
    borderTopWidth: 1,
    borderTopColor: COLORS.border,
  },
  
  subcategoriesContent: {
    paddingHorizontal: 20,
    gap: 8,
  },
  
  subcategoryItem: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 15,
    backgroundColor: COLORS.card,
    borderWidth: 1,
    borderColor: COLORS.border,
    marginRight: 8,
  },
  
  subcategoryText: {
    fontSize: 12,
    color: COLORS.textLight,
    fontWeight: '400',
  },
};
