{"version": 3, "file": "RNWView.js", "sourceRoot": "", "sources": ["../../src/primitives/RNWView.tsx"], "names": [], "mappings": "AAAA;;;;;;;GAOG;AACH,OAAO,KAAK,KAAK,MAAM,OAAO,CAAC;AAC/B,OAAO,UAAU,MAAM,0CAA0C,CAAC;AAClE,OAAO,mBAAmB,MAAM,wDAAwD,CAAC;AACzF,OAAO,aAAa,MAAM,6CAA6C,CAAC;AACxE,OAAO,KAAK,cAAc,MAAM,8CAA8C,CAAC;AAC/E,OAAO,IAAI,MAAM,oCAAoC,CAAC;AACtD,OAAO,gBAAgB,MAAM,gDAAgD,CAAC;AAC9E,OAAO,YAAY,MAAM,4CAA4C,CAAC;AACtE,OAAO,kBAAkB,MAAM,kDAAkD,CAAC;AAClF,OAAO,kBAAkB,MAAM,kDAAkD,CAAC;AAGlF,MAAM,gBAAgB,GAAG;IACvB,GAAG,cAAc,CAAC,YAAY;IAC9B,GAAG,cAAc,CAAC,kBAAkB;IACpC,GAAG,cAAc,CAAC,UAAU;IAC5B,GAAG,cAAc,CAAC,UAAU;IAC5B,GAAG,cAAc,CAAC,aAAa;IAC/B,GAAG,cAAc,CAAC,UAAU;IAC5B,GAAG,cAAc,CAAC,UAAU;IAC5B,GAAG,cAAc,CAAC,UAAU;IAC5B,IAAI,EAAE,IAAI;IACV,QAAQ,EAAE,IAAI;IACd,OAAO,EAAE,IAAI;IACb,aAAa,EAAE,IAAI;CACpB,CAAC;AAEF,MAAM,SAAS,GAAG,CAAC,KAAK,EAAE,EAAE,CAAC,IAAI,CAAC,KAAK,EAAE,gBAAgB,CAAC,CAAC;AAE3D;;;;GAIG;AAEH,aAAa;AACb,MAAM,IAAI,GAAsE,KAAK,CAAC,UAAU,CAC9F,CAAC,KAAK,EAAE,YAAY,EAAE,EAAE;IACtB,MAAM,EACJ,QAAQ,EACR,wBAAwB,EACxB,+BAA+B,EAC/B,cAAc,EACd,gBAAgB,EAChB,eAAe,EACf,iBAAiB,EACjB,kBAAkB,EAClB,gBAAgB,EAChB,oBAAoB,EACpB,6BAA6B,EAC7B,0BAA0B,EAC1B,iCAAiC,EACjC,mCAAmC,EACnC,0CAA0C,EAC1C,yBAAyB,EACzB,gCAAgC,EAChC,SAAS,GACV,GAAG,KAAY,CAAC;IAEjB,MAAM,eAAe,GAAG,KAAK,CAAC,UAAU,CAAC,mBAAmB,CAAC,CAAC;IAC9D,MAAM,OAAO,GAAG,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;IAEnC,gBAAgB,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;IACpC,kBAAkB,CAAC,OAAO,EAAE;QAC1B,wBAAwB;QACxB,+BAA+B;QAC/B,cAAc;QACd,gBAAgB;QAChB,eAAe;QACf,iBAAiB;QACjB,kBAAkB;QAClB,gBAAgB;QAChB,oBAAoB;QACpB,6BAA6B;QAC7B,0BAA0B;QAC1B,iCAAiC;QACjC,mCAAmC;QACnC,0CAA0C;QAC1C,yBAAyB;QACzB,gCAAgC;KACjC,CAAC,CAAC;IAEH,MAAM,KAAK,GAAG,UAAU,CAAC,OAAO,CAC9B,eAAe,IAAI,MAAM,CAAC,MAAM;IAChC,sBAAsB;IACtB,KAAK,CAAC,KAAK,CACZ,CAAC;IAEF,MAAM,cAAc,GAAG,SAAS,CAAC,KAAK,CAAC,CAAC;IACxC,cAAc,CAAC,KAAK,GAAG,KAAK,CAAC;IAE7B,MAAM,kBAAkB,GAAG,kBAAkB,CAAC,cAAc,CAAC,CAAC;IAC9D,MAAM,MAAM,GAAG,YAAY,CAAC,OAAO,EAAE,kBAAkB,EAAE,YAAY,CAAC,CAAC;IAEvE,cAAc,CAAC,GAAG,GAAG,MAAM,CAAC;IAE5B,OAAO,aAAa,CAAC,SAAS,EAAE,cAAc,CAAC,CAAC;AAClD,CAAC,CACF,CAAC;AAEF,IAAI,CAAC,WAAW,GAAG,MAAM,CAAC;AAE1B,MAAM,MAAM,GAAG,UAAU,CAAC,MAAM,CAAC;IAC/B,IAAI,EAAE;QACJ,UAAU,EAAE,SAAS;QACrB,MAAM,EAAE,eAAe;QACvB,SAAS,EAAE,YAAY;QACvB,OAAO,EAAE,MAAM;QACf,SAAS,EAAE,MAAM;QACjB,aAAa,EAAE,QAAQ;QACvB,UAAU,EAAE,CAAC;QACb,MAAM,EAAE,CAAC;QACT,SAAS,EAAE,CAAC;QACZ,QAAQ,EAAE,CAAC;QACX,OAAO,EAAE,CAAC;QACV,QAAQ,EAAE,UAAU;QACpB,MAAM,EAAE,CAAC;KACV;IACD,MAAM,EAAE;QACN,OAAO,EAAE,aAAa;KACvB;CACF,CAAC,CAAC;AAEH,eAAe,IAAI,CAAC", "sourcesContent": ["/**\n * Copyright (c) Expo.\n * Copyright (c) <PERSON>.\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\nimport * as React from 'react';\nimport StyleSheet from 'react-native-web/dist/exports/StyleSheet';\nimport TextAncestorContext from 'react-native-web/dist/exports/Text/TextAncestorContext';\nimport createElement from 'react-native-web/dist/exports/createElement';\nimport * as forwardedProps from 'react-native-web/dist/modules/forwardedProps';\nimport pick from 'react-native-web/dist/modules/pick';\nimport useElementLayout from 'react-native-web/dist/modules/useElementLayout';\nimport useMergeRefs from 'react-native-web/dist/modules/useMergeRefs';\nimport usePlatformMethods from 'react-native-web/dist/modules/usePlatformMethods';\nimport useResponderEvents from 'react-native-web/dist/modules/useResponderEvents';\nimport { PlatformMethods, ViewProps } from 'react-native-web/dist/types';\n\nconst forwardPropsList = {\n  ...forwardedProps.defaultProps,\n  ...forwardedProps.accessibilityProps,\n  ...forwardedProps.clickProps,\n  ...forwardedProps.focusProps,\n  ...forwardedProps.keyboardProps,\n  ...forwardedProps.mouseProps,\n  ...forwardedProps.touchProps,\n  ...forwardedProps.styleProps,\n  lang: true,\n  onScroll: true,\n  onWheel: true,\n  pointerEvents: true,\n};\n\nconst pickProps = (props) => pick(props, forwardPropsList);\n\n/**\n * This is the View from react-native-web copied out in order to supply a custom `__element` property.\n * In the past, you could use `createElement` to create an element with a custom HTML element, but this changed\n * somewhere between 0.14...0.17.\n */\n\n// @ts-ignore\nconst View: React.AbstractComponent<ViewProps, HTMLElement & PlatformMethods> = React.forwardRef(\n  (props, forwardedRef) => {\n    const {\n      onLayout,\n      onMoveShouldSetResponder,\n      onMoveShouldSetResponderCapture,\n      onResponderEnd,\n      onResponderGrant,\n      onResponderMove,\n      onResponderReject,\n      onResponderRelease,\n      onResponderStart,\n      onResponderTerminate,\n      onResponderTerminationRequest,\n      onScrollShouldSetResponder,\n      onScrollShouldSetResponderCapture,\n      onSelectionChangeShouldSetResponder,\n      onSelectionChangeShouldSetResponderCapture,\n      onStartShouldSetResponder,\n      onStartShouldSetResponderCapture,\n      __element,\n    } = props as any;\n\n    const hasTextAncestor = React.useContext(TextAncestorContext);\n    const hostRef = React.useRef(null);\n\n    useElementLayout(hostRef, onLayout);\n    useResponderEvents(hostRef, {\n      onMoveShouldSetResponder,\n      onMoveShouldSetResponderCapture,\n      onResponderEnd,\n      onResponderGrant,\n      onResponderMove,\n      onResponderReject,\n      onResponderRelease,\n      onResponderStart,\n      onResponderTerminate,\n      onResponderTerminationRequest,\n      onScrollShouldSetResponder,\n      onScrollShouldSetResponderCapture,\n      onSelectionChangeShouldSetResponder,\n      onSelectionChangeShouldSetResponderCapture,\n      onStartShouldSetResponder,\n      onStartShouldSetResponderCapture,\n    });\n\n    const style = StyleSheet.compose(\n      hasTextAncestor && styles.inline,\n      // @ts-ignore: untyped\n      props.style\n    );\n\n    const supportedProps = pickProps(props);\n    supportedProps.style = style;\n\n    const platformMethodsRef = usePlatformMethods(supportedProps);\n    const setRef = useMergeRefs(hostRef, platformMethodsRef, forwardedRef);\n\n    supportedProps.ref = setRef;\n\n    return createElement(__element, supportedProps);\n  }\n);\n\nView.displayName = 'View';\n\nconst styles = StyleSheet.create({\n  view: {\n    alignItems: 'stretch',\n    border: '0 solid black',\n    boxSizing: 'border-box',\n    display: 'flex',\n    flexBasis: 'auto',\n    flexDirection: 'column',\n    flexShrink: 0,\n    margin: 0,\n    minHeight: 0,\n    minWidth: 0,\n    padding: 0,\n    position: 'relative',\n    zIndex: 0,\n  },\n  inline: {\n    display: 'inline-flex',\n  },\n});\n\nexport default View;\n"]}