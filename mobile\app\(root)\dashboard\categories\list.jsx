import React, { useState, useEffect } from 'react';
import { View, Text, FlatList, TouchableOpacity, Alert } from 'react-native';
import { useUser } from '@clerk/clerk-expo';
import { useRouter } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import { COLORS } from '@/constants/colors';
import { API_URL } from '@/constants/api';
import { Navbar } from '../../../../components/Navbar';
import { styles } from '../../../../assets/styles/dashboard.styles';

export default function CategoriesList() {
  const { user } = useUser();
  const router = useRouter();
  const [searchQuery, setSearchQuery] = useState('');
  const [categories, setCategories] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchCategories();
  }, []);

  const fetchCategories = async () => {
    try {
      setLoading(true);
      const response = await fetch(`${API_URL}/categories`);
      const data = await response.json();
      setCategories(data);
    } catch (error) {
      console.error('Error fetching categories:', error);
      Alert.alert('Hata', 'Kategoriler yüklenirken bir hata oluştu');
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteCategory = async (categoryId) => {
    Alert.alert(
      'Kategoriyi Sil',
      'Bu kategoriyi silmek istediğinizden emin misiniz?',
      [
        { text: 'İptal', style: 'cancel' },
        {
          text: 'Sil',
          style: 'destructive',
          onPress: async () => {
            try {
              const response = await fetch(`${API_URL}/categories/${categoryId}`, {
                method: 'DELETE',
              });
              
              if (response.ok) {
                Alert.alert('Başarılı', 'Kategori başarıyla silindi');
                fetchCategories(); // Listeyi yenile
              } else {
                const errorData = await response.json();
                Alert.alert('Hata', errorData.message || 'Kategori silinirken bir hata oluştu');
              }
            } catch (error) {
              console.error('Error deleting category:', error);
              Alert.alert('Hata', 'Kategori silinirken bir hata oluştu');
            }
          }
        }
      ]
    );
  };

  const renderCategoryItem = ({ item }) => (
    <View style={styles.listItem}>
      <View style={styles.listItemContent}>
        <Text style={styles.listItemTitle}>{item.name}</Text>
        <Text style={styles.listItemSubtitle}>
          Oluşturulma: {new Date(item.created_at).toLocaleDateString('tr-TR')}
        </Text>
        <Text style={styles.listItemSubtitle}>
          Güncellenme: {new Date(item.updated_at).toLocaleDateString('tr-TR')}
        </Text>
      </View>
      
      <View style={styles.listItemActions}>
        <TouchableOpacity
          style={[styles.actionButton, styles.editButton]}
          onPress={() => router.push(`/dashboard/categories/edit/${item.id}`)}
        >
          <Ionicons name="pencil" size={16} color={COLORS.white} />
        </TouchableOpacity>
        
        <TouchableOpacity
          style={[styles.actionButton, styles.deleteButton]}
          onPress={() => handleDeleteCategory(item.id)}
        >
          <Ionicons name="trash" size={16} color={COLORS.white} />
        </TouchableOpacity>
      </View>
    </View>
  );

  const filteredCategories = categories.filter(category =>
    category.name.toLowerCase().includes(searchQuery.toLowerCase())
  );

  return (
    <View style={styles.container}>
      <Navbar 
        user={user} 
        searchQuery={searchQuery} 
        setSearchQuery={setSearchQuery}
        router={router}
      />
      
      <View style={styles.pageContent}>
        <View style={styles.pageHeader}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => router.back()}
          >
            <Ionicons name="arrow-back" size={24} color={COLORS.text} />
          </TouchableOpacity>
          
          <Text style={styles.pageTitle}>Kategoriler Listesi</Text>
          
          <TouchableOpacity
            style={styles.addButton}
            onPress={() => router.push('/dashboard/categories/add')}
          >
            <Ionicons name="add" size={24} color={COLORS.white} />
            <Text style={styles.addButtonText}>Yeni Kategori</Text>
          </TouchableOpacity>
        </View>

        {loading ? (
          <View style={styles.loadingContainer}>
            <Text style={styles.loadingText}>Yükleniyor...</Text>
          </View>
        ) : (
          <FlatList
            data={filteredCategories}
            renderItem={renderCategoryItem}
            keyExtractor={(item) => item.id.toString()}
            style={styles.list}
            contentContainerStyle={styles.listContent}
            showsVerticalScrollIndicator={false}
            ListEmptyComponent={
              <View style={styles.emptyContainer}>
                <Ionicons name="list-outline" size={64} color={COLORS.textLight} />
                <Text style={styles.emptyText}>Henüz kategori bulunmuyor</Text>
                <TouchableOpacity
                  style={styles.emptyButton}
                  onPress={() => router.push('/dashboard/categories/add')}
                >
                  <Text style={styles.emptyButtonText}>İlk Kategoriyi Ekle</Text>
                </TouchableOpacity>
              </View>
            }
          />
        )}
      </View>
    </View>
  );
}
