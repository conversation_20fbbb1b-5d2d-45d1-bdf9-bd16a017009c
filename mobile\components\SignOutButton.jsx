import * as Linking from 'expo-linking'
import { Text, TouchableOpacity, Platform } from 'react-native'
import {styles} from '../assets/styles/home.styles'
import { Ionicons } from '@expo/vector-icons'
import { COLORS } from '../constants/colors'
import { Alert } from 'react-native'

// Mock signOut function for web
const useMockClerk = () => ({
  signOut: async () => {
    console.log('Mock sign out');
    // For web, just reload the page or redirect
    if (Platform.OS === 'web') {
      window.location.reload();
    }
  }
});

export const SignOutButton = () => {
  // Use mock clerk for web compatibility
  const { signOut } = useMockClerk()
  const handleSignOut = async () => {
    if (Platform.OS === 'web') {
      // Web için confirm dialog kullan
      const confirmed = window.confirm('Are you sure you want to sign out?');
      if (confirmed) {
        try {
          await signOut()
        } catch (err) {
          console.error(JSON.stringify(err, null, 2))
        }
      }
    } else {
      // Mobile için Alert kullan
      Alert.alert(
        'Sign out',
        'Are you sure you want to sign out?',
        [
          {
            text: 'Cancel',
            style: 'cancel',
          },
          {
            text: 'Sign out',
            onPress: async () => {
              try {
                await signOut()
              } catch (err) {
                console.error(JSON.stringify(err, null, 2))
              }
            },
          },
        ],
      )
    }
  }
  return (
    <TouchableOpacity style={styles.logoutButton} onPress={handleSignOut}>
      <Ionicons name="log-out-outline" size={24} color={COLORS.text}/>
    </TouchableOpacity>
  )
}