import * as Linking from 'expo-linking'
import { Text, TouchableOpacity, Platform } from 'react-native'
import {styles} from '../assets/styles/home.styles'
import { Ionicons } from '@expo/vector-icons'
import { COLORS } from '../constants/colors'
import { Alert } from 'react-native'

// Conditional import for useClerk
let useClerk;
if (Platform.OS === 'web') {
  // Mock clerk for web
  useClerk = () => ({
    signOut: async () => {
      console.log('Mock sign out');
      window.location.reload();
    }
  });
} else {
  try {
    const clerk = require('@clerk/clerk-expo');
    useClerk = clerk.useClerk;
  } catch (error) {
    // Fallback to mock
    useClerk = () => ({
      signOut: async () => {
        console.log('Mock sign out');
      }
    });
  }
}

export const SignOutButton = () => {
  // Use conditional clerk
  const { signOut } = useClerk()
  const handleSignOut = async () => {
    if (Platform.OS === 'web') {
      // Web için confirm dialog kullan
      const confirmed = window.confirm('Are you sure you want to sign out?');
      if (confirmed) {
        try {
          await signOut()
        } catch (err) {
          console.error(JSON.stringify(err, null, 2))
        }
      }
    } else {
      // Mobile için Alert kullan
      Alert.alert(
        'Sign out',
        'Are you sure you want to sign out?',
        [
          {
            text: 'Cancel',
            style: 'cancel',
          },
          {
            text: 'Sign out',
            onPress: async () => {
              try {
                await signOut()
              } catch (err) {
                console.error(JSON.stringify(err, null, 2))
              }
            },
          },
        ],
      )
    }
  }
  return (
    <TouchableOpacity style={styles.logoutButton} onPress={handleSignOut}>
      <Ionicons name="log-out-outline" size={24} color={COLORS.text}/>
    </TouchableOpacity>
  )
}