import { useClerk } from '@clerk/clerk-expo'
import * as Linking from 'expo-linking'
import { Text, TouchableOpacity, Platform } from 'react-native'
import {styles} from '../assets/styles/home.styles'
import { Ionicons } from '@expo/vector-icons'
import { COLORS } from '../constants/colors'
import { Alert } from 'react-native'

export const SignOutButton = () => {
  // Use `useClerk()` to access the `signOut()` function
  const { signOut } = useClerk()
  const handleSignOut = async () => {
    if (Platform.OS === 'web') {
      // Web için confirm dialog kullan
      const confirmed = window.confirm('Are you sure you want to sign out?');
      if (confirmed) {
        try {
          await signOut()
        } catch (err) {
          console.error(JSON.stringify(err, null, 2))
        }
      }
    } else {
      // Mobile için Alert kullan
      Alert.alert(
        'Sign out',
        'Are you sure you want to sign out?',
        [
          {
            text: 'Cancel',
            style: 'cancel',
          },
          {
            text: 'Sign out',
            onPress: async () => {
              try {
                await signOut()
              } catch (err) {
                console.error(JSON.stringify(err, null, 2))
              }
            },
          },
        ],
      )
    }
  }
  return (
    <TouchableOpacity style={styles.logoutButton} onPress={handleSignOut}>
      <Ionicons name="log-out-outline" size={24} color={COLORS.text}/>
    </TouchableOpacity>
  )
}