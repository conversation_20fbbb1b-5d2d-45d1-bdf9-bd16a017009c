{"version": 3, "file": "ExpoApplication.web.js", "sourceRoot": "", "sources": ["../src/ExpoApplication.web.ts"], "names": [], "mappings": "AAAA,eAAe;IACb,IAAI,eAAe;QACjB,OAAO,IAAI,CAAC;IACd,CAAC;IACD,IAAI,QAAQ;QACV,OAAO,IAAI,CAAC;IACd,CAAC;IACD,IAAI,wBAAwB;QAC1B,OAAO,IAAI,CAAC;IACd,CAAC;IACD,IAAI,kBAAkB;QACpB,OAAO,IAAI,CAAC;IACd,CAAC;IACD,IAAI,SAAS;QACX,OAAO,IAAI,CAAC;IACd,CAAC;IACD,KAAK,CAAC,wBAAwB;QAC5B,OAAO,IAAI,CAAC;IACd,CAAC;CACF,CAAC", "sourcesContent": ["export default {\n  get applicationName(): null {\n    return null;\n  },\n  get bundleId(): null {\n    return null;\n  },\n  get nativeApplicationVersion(): null {\n    return null;\n  },\n  get nativeBuildVersion(): null {\n    return null;\n  },\n  get androidId(): null {\n    return null;\n  },\n  async getInstallationTimeAsync(): Promise<null> {\n    return null;\n  },\n};\n"]}