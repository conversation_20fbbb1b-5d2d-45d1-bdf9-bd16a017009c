{"name": "@expo/html-elements", "version": "0.4.2", "description": "Universal semantic HTML React components for iOS, Android, web, and desktop", "main": "build/Elements.js", "types": "build/Elements.d.ts", "sideEffects": false, "scripts": {"build": "expo-module build", "clean": "expo-module clean", "lint": "expo-module lint", "test": "yarn test:babel && yarn test:src", "test:src": "expo-module test", "test:babel": "jest --config babel/jest.config.js", "prepare": "expo-module prepare", "prepublishOnly": "expo-module prepublishOnly", "expo-module": "expo-module"}, "keywords": ["expo", "expo-web", "react-native", "react-native-web", "html"], "repository": {"type": "git", "url": "https://github.com/expo/expo.git", "directory": "packages/html-elements"}, "bugs": {"url": "https://github.com/expo/expo/issues"}, "author": "650 Industries, Inc.", "license": "MIT", "homepage": "https://github.com/expo/expo/tree/main/packages/html-elements", "jest": {"projects": [{"preset": "jest-expo/ios", "modulePathIgnorePatterns": ["<rootDir>/babel/"]}, {"preset": "jest-expo/android", "modulePathIgnorePatterns": ["<rootDir>/babel/"]}, {"preset": "jest-expo/web", "modulePathIgnorePatterns": ["<rootDir>/babel/"]}]}, "devDependencies": {"expo-module-scripts": "^3.0.0"}}