import { Slot } from "expo-router";
import "@/global.css";
import { GluestackUIProvider } from "@/components/ui/gluestack-ui-provider";
import SafeScreen from "../components/SafeScreen";
import { Platform } from 'react-native';

// Conditional imports for different platforms
let ClerkProvider, tokenCache;

if (Platform.OS !== 'web') {
  try {
    const clerk = require('@clerk/clerk-expo');
    ClerkProvider = clerk.ClerkProvider;
    tokenCache = clerk.tokenCache;
  } catch (error) {
    console.log('Clerk not available for this platform');
  }
}

const publishableKey = process.env.EXPO_PUBLIC_CLERK_PUBLISHABLE_KEY;

export default function Rootlayout() {
  const content = (
    <SafeScreen>
      <Slot />
    </SafeScreen>
  );

  if (Platform.OS === 'web' || !ClerkProvider) {
    // For web or when Clerk is not available, use direct content
    return (
      <GluestackUIProvider mode="light">
        {content}
      </GluestackUIProvider>
    );
  }

  // For mobile platforms, use Clerk
  if (!publishableKey) {
    console.warn('Missing Publishable Key. Please set EXPO_PUBLIC_CLERK_PUBLISHABLE_KEY in your .env');
    return (
      <GluestackUIProvider mode="light">
        {content}
      </GluestackUIProvider>
    );
  }

  return (
    <GluestackUIProvider mode="light">
      <ClerkProvider tokenCache={tokenCache} publishableKey={publishableKey}>
        {content}
      </ClerkProvider>
    </GluestackUIProvider>
  );
}
