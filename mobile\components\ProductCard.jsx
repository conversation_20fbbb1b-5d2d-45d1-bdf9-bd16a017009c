import React from 'react';
import { TouchableOpacity } from 'react-native';
import { Box } from "@/components/ui/box";
import { Button, ButtonText } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Heading } from "@/components/ui/heading";
import { Image } from "@/components/ui/image";
import { Text } from "@/components/ui/text";
import { VStack } from "@/components/ui/vstack";
import { HStack } from "@/components/ui/hstack";
import { Ionicons } from '@expo/vector-icons';
import { COLORS } from '../constants/colors';

export const ProductCard = ({ product, onPress, onAddToCart, onAddToWishlist }) => {
  const discountPercentage = product.original_price > product.price
    ? Math.round(((product.original_price - product.price) / product.original_price) * 100)
    : 0;

  const handleCardPress = () => {
    onPress && onPress(product);
  };

  const handleAddToCart = (e) => {
    e.stopPropagation();
    onAddToCart && onAddToCart(product);
  };

  const handleAddToWishlist = (e) => {
    e.stopPropagation();
    onAddToWishlist && onAddToWishlist(product);
  };

  return (
    <TouchableOpacity onPress={handleCardPress} activeOpacity={0.9}>
      <Card className="p-4 rounded-xl max-w-[280px] m-2 bg-white shadow-sm border border-gray-200">
        <Box className="relative">
          <Image
            source={{
              uri: product.images && product.images.length > 0
                ? (typeof product.images === 'string' ? JSON.parse(product.images)[0] : product.images[0])
                : 'https://via.placeholder.com/240x180/f0f0f0/999999?text=No+Image',
            }}
            className="mb-4 h-[180px] w-full rounded-lg"
            alt={product.name}
            style={{ resizeMode: 'cover' }}
          />

          {/* Discount Badge */}
          {discountPercentage > 0 && (
            <Box className="absolute top-2 left-2 bg-red-500 px-2 py-1 rounded-md">
              <Text className="text-white text-xs font-bold">
                -%{discountPercentage}
              </Text>
            </Box>
          )}

          {/* Featured Badge */}
          {product.featured && (
            <Box className="absolute top-2 right-2 bg-yellow-500 px-2 py-1 rounded-md">
              <Text className="text-white text-xs font-bold">
                ⭐ Öne Çıkan
              </Text>
            </Box>
          )}

          {/* Wishlist Button */}
          <TouchableOpacity
            style={styles.wishlistButton}
            onPress={handleAddToWishlist}
          >
            <Ionicons name="heart-outline" size={20} color={COLORS.text} />
          </TouchableOpacity>
        </Box>

        <Text className="text-sm font-normal mb-2 text-gray-600">
          {product.category}
        </Text>

        <VStack className="mb-4">
          <Heading size="md" className="mb-2 text-gray-900" numberOfLines={2}>
            {product.name}
          </Heading>
          <Text size="sm" className="text-gray-600" numberOfLines={3}>
            {product.description}
          </Text>
        </VStack>

        {/* Price Section */}
        <VStack className="mb-4">
          <HStack className="items-center mb-2">
            <Text className="text-lg font-bold text-green-600 mr-2">
              ₺{parseFloat(product.price).toLocaleString('tr-TR', { minimumFractionDigits: 2 })}
            </Text>
            {discountPercentage > 0 && (
              <Text className="text-sm text-gray-400 line-through">
                ₺{parseFloat(product.original_price).toLocaleString('tr-TR', { minimumFractionDigits: 2 })}
              </Text>
            )}
          </HStack>

          {/* Stock Info */}
          <HStack className="items-center">
            <Box
              className={`w-2 h-2 rounded-full mr-2 ${
                product.stock > 10 ? 'bg-green-500' :
                product.stock > 0 ? 'bg-yellow-500' : 'bg-red-500'
              }`}
            />
            <Text className="text-xs text-gray-500">
              {product.stock > 0 ? `${product.stock} adet stokta` : 'Stokta yok'}
            </Text>
          </HStack>
        </VStack>

        {/* Action Buttons */}
        <Box className="flex-col sm:flex-row gap-2">
          <Button
            className={`px-4 py-2 flex-1 ${
              product.stock > 0 ? 'bg-blue-600' : 'bg-gray-400'
            }`}
            onPress={handleAddToCart}
            disabled={product.stock === 0}
          >
            <ButtonText size="sm" className="text-white">
              {product.stock > 0 ? 'Sepete Ekle' : 'Stokta Yok'}
            </ButtonText>
          </Button>

          <Button
            variant="outline"
            className="px-4 py-2 border-gray-300 flex-1"
            onPress={handleCardPress}
          >
            <ButtonText size="sm" className="text-gray-600">
              Detaylar
            </ButtonText>
          </Button>
        </Box>
      </Card>
    </TouchableOpacity>
  );
};

const styles = {
  wishlistButton: {
    position: 'absolute',
    bottom: 8,
    right: 8,
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    borderRadius: 20,
    padding: 8,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 3,
  },
};


