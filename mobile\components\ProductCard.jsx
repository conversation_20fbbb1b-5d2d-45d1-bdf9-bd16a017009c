
import React from 'react'
import { View, Text, TouchableOpacity, Image } from 'react-native'
import {styles} from '../assets/styles/home.styles'
import { COLORS } from '../constants/colors'
import { Ionicons } from '@expo/vector-icons'
import { useNavigation } from '@react-navigation/native'
import { formatPrice } from '../lib/utils'

export const ProductCard = ({ product }) => {
  const navigation = useNavigation()

  return (
    <TouchableOpacity
      style={styles.productCard}
      onPress={() => navigation.navigate('Product', { product })}
    >
      <Image
        source={{ uri: product.images[0] }}
        style={styles.productImage}
      />
      <View style={styles.productContent}>
        <Text style={styles.productTitle}>{product.name}</Text>
        <Text style={styles.productPrice}>{formatPrice(product.price)}</Text>
      </View>
    </TouchableOpacity>
  )
}

