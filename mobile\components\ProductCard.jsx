import React from 'react';
import { TouchableOpacity } from 'react-native';
import { Box } from "@/components/ui/box";
import { Button, ButtonText } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Heading } from "@/components/ui/heading";
import { Image } from "@/components/ui/image";
import { Text } from "@/components/ui/text";
import { VStack } from "@/components/ui/vstack";
import { HStack } from "@/components/ui/hstack";
import { Ionicons } from '@expo/vector-icons';
import { COLORS } from '../constants/colors';
import { useProducts } from '../hooks/useProducts';

// Safe function to get product image






export const ProductCard = ({ product }) => {

  const { getImageUrl } = useProducts();


    return (
    <Card className="p-5 rounded-lg max-w-[360px] m-3">
      <Image
        source={{
          uri: getImageUrl(product)
        }}
        className="mb-6 h-[240px] w-full rounded-md aspect-[4/3]"
        alt="image"
      />
      <Text
        className="text-sm font-normal mb-2 text-typography-700"
      >
        {product.category}
      </Text>
      <VStack className="mb-6">
        <Heading size="md" className="mb-4">
          {product.name}
        </Heading>
        <Text size="sm">
          Floral embroidered notch neck thread work cotton kurta in white and
          black.
        </Text>
      </VStack>
      <Box
        className="flex-col sm:flex-row"
      >
        <Button
          className="px-4 py-2 mr-0 mb-3 sm:mr-3 sm:mb-0 sm:flex-1"
        >
          <ButtonText size="sm">Add to cart</ButtonText>
        </Button>
        <Button
          variant="outline"
          className="px-4 py-2 border-outline-300 sm:flex-1"
        >
          <ButtonText
            size="sm"
            className="text-typography-600"
          >
            Wishlist
          </ButtonText>
        </Button>
      </Box>
    </Card>
);
}