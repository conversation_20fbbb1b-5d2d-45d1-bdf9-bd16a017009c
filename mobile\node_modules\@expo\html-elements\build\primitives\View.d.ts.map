{"version": 3, "file": "View.d.ts", "sourceRoot": "", "sources": ["../../src/primitives/View.tsx"], "names": [], "mappings": "AAAA,OAAO,EAAE,eAAe,EAAE,cAAc,EAAE,aAAa,EAAE,MAAM,OAAO,CAAC;AACvE,OAAO,EACL,iBAAiB,EACjB,SAAS,EACT,IAAI,IAAI,UAAU,EAClB,SAAS,IAAI,eAAe,EAC7B,MAAM,cAAc,CAAC;AAOtB,KAAK,eAAe,GAAG,cAAc,CAAC,OAAO,UAAU,CAAC,GAAG,eAAe,CAAC,OAAO,UAAU,CAAC,CAAC;AAE9F;;GAEG;AACH,MAAM,WAAW,YAAY;IAC3B,oBAAoB;IACpB,cAAc,CAAC,EAAE,MAAM,CAAC;IACxB,oBAAoB;IACpB,cAAc,CAAC,EAAE,MAAM,CAAC;IACxB,oBAAoB;IACpB,kBAAkB,CAAC,EAAE,MAAM,CAAC;IAC5B,oBAAoB;IACpB,iBAAiB,CAAC,EAAE,MAAM,CAAC;IAC3B,oBAAoB;IACpB,iBAAiB,CAAC,EAAE,MAAM,CAAC;IAC3B,oBAAoB;IACpB,aAAa,CAAC,EAAE,MAAM,GAAG,GAAG,EAAE,CAAC;IAC/B,oBAAoB;IACpB,uBAAuB,CAAC,EAAE,MAAM,GAAG,UAAU,CAAC;IAC9C,oBAAoB;IACpB,kBAAkB,CAAC,EAAE,MAAM,CAAC;IAC5B,oBAAoB;IACpB,uBAAuB,CAAC,EAAE,MAAM,CAAC;IACjC,oBAAoB;IACpB,oBAAoB,CAAC,EAAE,MAAM,CAAC;IAC9B,oBAAoB;IACpB,mBAAmB,CAAC,EAAE,MAAM,CAAC;IAC7B,oBAAoB;IACpB,cAAc,CAAC,EAAE,MAAM,CAAC;IACxB,oBAAoB;IACpB,eAAe,CAAC,EAAE,MAAM,CAAC;IACzB,oBAAoB;IACpB,gBAAgB,CAAC,EAAE,YAAY,GAAG,aAAa,GAAG,aAAa,CAAC;IAChE,oBAAoB;IACpB,kBAAkB,CAAC,EAAE,MAAM,CAAC;IAC5B,oBAAoB;IACpB,gBAAgB,CAAC,EAAE,MAAM,CAAC;IAC1B,oBAAoB;IACpB,cAAc,CAAC,EAAE,MAAM,CAAC;IACxB,oBAAoB;IACpB,SAAS,CAAC,EAAE,MAAM,CAAC;IACnB,oBAAoB;IACpB,SAAS,CAAC,EAAE,MAAM,CAAC;IACnB,oBAAoB;IACpB,IAAI,CAAC,EAAE,MAAM,CAAC;IACd,oBAAoB;IACpB,MAAM,CAAC,EAAE,MAAM,CAAC;IAChB,oBAAoB;IACpB,MAAM,CAAC,EAAE,MAAM,CAAC;IAChB,oBAAoB;IACpB,eAAe,CAAC,EAAE,MAAM,CAAC;IACzB,oBAAoB;IACpB,YAAY,CAAC,EAAE,MAAM,CAAC;IACtB,oBAAoB;IACpB,YAAY,CAAC,EAAE,MAAM,CAAC;IACtB,oBAAoB;IACpB,aAAa,CAAC,EAAE,MAAM,CAAC;IACvB,oBAAoB;IACpB,aAAa,CAAC,EAAE,MAAM,CAAC;IACvB,oBAAoB;IACpB,eAAe,CAAC,EAAE,MAAM,CAAC;IACzB,oBAAoB;IACpB,UAAU,CAAC,EAAE,MAAM,CAAC;IACpB,oBAAoB;IACpB,UAAU,CAAC,EAAE,MAAM,CAAC;IACpB,oBAAoB;IACpB,YAAY,CAAC,EAAE,MAAM,CAAC;IACtB,oBAAoB;IACpB,mBAAmB,CAAC,EAAE,MAAM,CAAC;IAC7B,oBAAoB;IACpB,gBAAgB,CAAC,EAAE,MAAM,CAAC;IAC1B,oBAAoB;IACpB,iBAAiB,CAAC,EAAE,MAAM,CAAC;IAC3B,oBAAoB;IACpB,OAAO,CAAC,EAAE,MAAM,CAAC;IACjB,oBAAoB;IACpB,YAAY,CAAC,EAAE,MAAM,CAAC;IACtB,oBAAoB;IACpB,SAAS,CAAC,EAAE,MAAM,CAAC;IACnB,oBAAoB;IACpB,SAAS,CAAC,EAAE,MAAM,CAAC;IACnB,oBAAoB;IACpB,kBAAkB,CAAC,EAAE,MAAM,GAAG,SAAS,GAAG,MAAM,CAAC;IACjD,oBAAoB;IACpB,mBAAmB,CAAC,EAAE,MAAM,GAAG,SAAS,GAAG,MAAM,CAAC;IAClD,oBAAoB;IACpB,mBAAmB,CAAC,EAAE,MAAM,GAAG,SAAS,GAAG,MAAM,CAAC;IAClD,oBAAoB;IACpB,WAAW,CAAC,EAAE,MAAM,CAAC;IACrB,oBAAoB;IACpB,iBAAiB,CAAC,EAAE,MAAM,CAAC;IAC3B,oBAAoB;IACpB,WAAW,CAAC,EAAE,MAAM,CAAC;IACrB,oBAAoB;IACpB,eAAe,CAAC,EAAE,MAAM,CAAC;IACzB,oBAAoB;IACpB,eAAe,CAAC,EAAE,MAAM,CAAC;IACzB,oBAAoB;IACpB,kBAAkB,CAAC,EAAE,MAAM,CAAC;IAC5B,oBAAoB;IACpB,kBAAkB,CAAC,EAAE,MAAM,CAAC;IAC5B,oBAAoB;IACpB,wBAAwB,CAAC,EAAE,MAAM,CAAC;IAClC,oBAAoB;IACpB,UAAU,CAAC,EAAE,MAAM,CAAC;IACpB,oBAAoB;IACpB,UAAU,CAAC,EAAE,MAAM,CAAC;IACpB,oBAAoB;IACpB,UAAU,CAAC,EAAE,MAAM,CAAC;IACpB,oBAAoB;IACpB,QAAQ,CAAC,EAAE,QAAQ,GAAG,UAAU,GAAG,UAAU,GAAG,OAAO,GAAG,QAAQ,CAAC;CACpE;AAED,MAAM,MAAM,SAAS,GAAG,IAAI,CAAC,eAAe,EAAE,UAAU,CAAC,GAAG,YAAY,CAAC;AAEzE,MAAM,MAAM,YAAY,GAAG;IACzB,KAAK,CAAC,EAAE,SAAS,CAAC,SAAS,CAAC,CAAC;IAE7B,iBAAiB,CAAC,EACd,MAAM,GACN,UAAU,GACV,eAAe,GACf,aAAa,GACb,QAAQ,GACR,YAAY,GACZ,MAAM,GACN,SAAS,GACT,QAAQ,GACR,iBAAiB,CAAC;CACvB,CAAC;AAEF,MAAM,MAAM,SAAS,GAAG,YAAY,GAAG,IAAI,CAAC,eAAe,EAAE,OAAO,GAAG,mBAAmB,CAAC,CAAC;;AAS5F,wBAAsE"}