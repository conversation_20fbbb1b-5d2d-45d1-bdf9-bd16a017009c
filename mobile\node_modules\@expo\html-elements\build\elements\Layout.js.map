{"version": 3, "file": "Layout.js", "sourceRoot": "", "sources": ["../../src/elements/Layout.tsx"], "names": [], "mappings": "AAAA,OAAO,KAAK,EAAE,EAAiB,UAAU,EAAE,MAAM,OAAO,CAAC;AACzD,OAAO,EAAE,QAAQ,EAAE,MAAM,cAAc,CAAC;AAExC,OAAO,IAAmB,MAAM,oBAAoB,CAAC;AAErD,SAAS,UAAU,CAAC,cAAyB,EAAE;IAC7C,OAAO,UAAU,CAAC,CAAC,KAAgB,EAAE,GAAG,EAAE,EAAE;QAC1C,OAAO,oBAAC,IAAI,OAAK,WAAW,KAAM,KAAK,EAAE,GAAG,EAAE,GAAG,GAAI,CAAC;IACxD,CAAC,CAA6B,CAAC;AACjC,CAAC;AAED,MAAM,CAAC,MAAM,GAAG,GAAG,UAAU,EAAE,CAAC;AAEhC,MAAM,CAAC,MAAM,GAAG,GAAG,UAAU,CAC3B,QAAQ,CAAC,MAAM,CAAC;IACd,GAAG,EAAE;QACH,iBAAiB,EAAE,YAAY;KAChC;CACF,CAAC,CACH,CAAC;AACF,MAAM,CAAC,MAAM,MAAM,GAAG,UAAU,CAC9B,QAAQ,CAAC,MAAM,CAAC;IACd,GAAG,EAAE;QACH,iBAAiB,EAAE,aAAa;KACjC;CACF,CAAC,CACH,CAAC;AACF,MAAM,CAAC,MAAM,KAAK,GAAG,UAAU,CAC7B,QAAQ,CAAC,MAAM,CAAC;IACd,GAAG,EAAE;QACH,iBAAiB,EAAE,eAAe;KACnC;CACF,CAAC,CACH,CAAC;AACF,MAAM,CAAC,MAAM,MAAM,GAAG,UAAU,CAC9B,QAAQ,CAAC,MAAM,CAAC;IACd,GAAG,EAAE;QACH,iBAAiB,EAAE,QAAQ;KAC5B;IACD,OAAO,EAAE;QACP,iBAAiB,EAAE,QAAQ;KAC5B;CACF,CAAC,CACH,CAAC;AACF,MAAM,CAAC,MAAM,IAAI,GAAG,UAAU,CAC5B,QAAQ,CAAC,MAAM,CAAC;IACd,GAAG,EAAE;QACH,iBAAiB,EAAE,MAAM;KAC1B;CACF,CAAC,CACH,CAAC;AACF,MAAM,CAAC,MAAM,OAAO,GAAG,UAAU,CAC/B,QAAQ,CAAC,MAAM,CAAC;IACd,GAAG,EAAE;QACH,iBAAiB,EAAE,SAAS;KAC7B;CACF,CAAC,CACH,CAAC;AACF,MAAM,CAAC,MAAM,OAAO,GAAG,UAAU,CAAC;IAChC,iBAAiB,EAAE,SAAS,EAAE,UAAU;CACzC,CAAC,CAAC", "sourcesContent": ["import React, { ComponentType, forwardRef } from 'react';\nimport { Platform } from 'react-native';\n\nimport View, { ViewProps } from '../primitives/View';\n\nfunction createView(nativeProps: ViewProps = {}): ComponentType<ViewProps> {\n  return forwardRef((props: ViewProps, ref) => {\n    return <View {...nativeProps} {...props} ref={ref} />;\n  }) as ComponentType<ViewProps>;\n}\n\nexport const Div = createView();\n\nexport const Nav = createView(\n  Platform.select({\n    web: {\n      accessibilityRole: 'navigation',\n    },\n  })\n);\nexport const Footer = createView(\n  Platform.select({\n    web: {\n      accessibilityRole: 'contentinfo',\n    },\n  })\n);\nexport const Aside = createView(\n  Platform.select({\n    web: {\n      accessibilityRole: 'complementary',\n    },\n  })\n);\nexport const Header = createView(\n  Platform.select({\n    web: {\n      accessibilityRole: 'banner',\n    },\n    default: {\n      accessibilityRole: 'header',\n    },\n  })\n);\nexport const Main = createView(\n  Platform.select({\n    web: {\n      accessibilityRole: 'main',\n    },\n  })\n);\nexport const Article = createView(\n  Platform.select({\n    web: {\n      accessibilityRole: 'article',\n    },\n  })\n);\nexport const Section = createView({\n  accessibilityRole: 'summary', // region?\n});\n"]}