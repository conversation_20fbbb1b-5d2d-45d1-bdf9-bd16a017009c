{"version": 3, "file": "createDevView.js", "sourceRoot": "", "sources": ["../../src/primitives/createDevView.tsx"], "names": [], "mappings": "AAAA,OAAO,KAAK,MAAM,OAAO,CAAC;AAC1B,OAAO,EAAE,QAAQ,EAAE,UAAU,EAAE,IAAI,EAAE,MAAM,cAAc,CAAC;AAE1D,SAAS,WAAW,CAAC,aAA8B;IACjD,OAAO,KAAK,CAAC,OAAO,CAAC,GAAG,EAAE;QACxB,MAAM,QAAQ,GAAsB,EAAE,CAAC;QACvC,KAAK,CAAC,QAAQ,CAAC,OAAO,CAAC,aAAa,EAAE,CAAC,KAAK,EAAE,EAAE;YAC9C,IAAI,KAAK,IAAI,IAAI,IAAI,OAAO,KAAK,KAAK,SAAS,EAAE;aAChD;iBAAM,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;gBACjE,iCAAiC;gBACjC,IAAI,OAAO,GAAG,yCAAyC,KAAK,IAC1D,KAAK,KAAK,EAAE,CAAC,CAAC,CAAC,iBAAiB,CAAC,CAAC,CAAC,EACrC,GAAG,CAAC;gBACJ,OAAO,IAAI,2DAA2D,CAAC;gBACvE,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;gBACtB,QAAQ,CAAC,IAAI,CACX,oBAAC,IAAI,IAAC,KAAK,EAAE,CAAC,UAAU,CAAC,YAAY,EAAE,MAAM,CAAC,KAAK,CAAC;;oBACjC,oBAAC,IAAI,IAAC,KAAK,EAAE,EAAE,UAAU,EAAE,MAAM,EAAE,IAAG,KAAK,CAAQ;yBAC/D,CACR,CAAC;aACH;iBAAM,IAAI,MAAM,IAAI,KAAK,IAAI,OAAO,KAAK,EAAE,IAAI,KAAK,QAAQ,IAAI,QAAQ,CAAC,EAAE,KAAK,KAAK,EAAE;gBACtF,uDAAuD;gBACvD,MAAM,IAAI,KAAK,CAAC,yCAAyC,KAAK,CAAC,IAAI,KAAK,CAAC,CAAC;aAC3E;iBAAM;gBACL,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;aACtB;QACH,CAAC,CAAC,CAAC;QACH,OAAO,QAAQ,CAAC;IAClB,CAAC,EAAE,CAAC,aAAa,CAAC,CAAC,CAAC;AACtB,CAAC;AAED,wFAAwF;AACxF,MAAM,UAAU,aAAa,CAAyC,IAAW;IAC/E,OAAO,KAAK,CAAC,UAAU,CAAC,CAAC,EAAE,QAAQ,EAAE,GAAG,KAAK,EAAO,EAAE,YAA8B,EAAE,EAAE;QACtF,OAAO,oBAAC,IAAI,IAAC,GAAG,EAAE,YAAY,KAAM,KAAK,EAAE,QAAQ,EAAE,WAAW,CAAC,QAAQ,CAAC,GAAI,CAAC;IACjF,CAAC,CAAC,CAAC;AACL,CAAC;AAED,MAAM,MAAM,GAAG,UAAU,CAAC,MAAM,CAAC;IAC/B,KAAK,EAAE;QACL,eAAe,EAAE,WAAW;QAC5B,KAAK,EAAE,OAAO;KACf;CACF,CAAC,CAAC", "sourcesContent": ["import React from 'react';\nimport { Platform, StyleSheet, Text } from 'react-native';\n\nfunction useChildren(inputChildren: React.ReactNode) {\n  return React.useMemo(() => {\n    const children: React.ReactNode[] = [];\n    React.Children.forEach(inputChildren, (child) => {\n      if (child == null || typeof child === 'boolean') {\n      } else if (typeof child === 'string' || typeof child === 'number') {\n        // Wrap text in a Text component.\n        let message = `Invalid raw text as a child of View: \"${child}\"${\n          child === '' ? ` [empty string]` : ''\n        }.`;\n        message += ' Wrap the text contents with a Text element or remove it.';\n        console.warn(message);\n        children.push(\n          <Text style={[StyleSheet.absoluteFill, styles.error]}>\n            Unwrapped text: \"<Text style={{ fontWeight: 'bold' }}>{child}</Text>\"\n          </Text>\n        );\n      } else if ('type' in child && typeof child?.type === 'string' && Platform.OS !== 'web') {\n        // Disallow untransformed react-dom elements on native.\n        throw new Error(`Using unsupported React DOM element: <${child.type} />`);\n      } else {\n        children.push(child);\n      }\n    });\n    return children;\n  }, [inputChildren]);\n}\n\n/** Extend a view with a `children` filter that asserts more helpful warnings/errors. */\nexport function createDevView<TView extends React.ComponentType<any>>(View: TView) {\n  return React.forwardRef(({ children, ...props }: any, forwardedRef: React.Ref<TView>) => {\n    return <View ref={forwardedRef} {...props} children={useChildren(children)} />;\n  });\n}\n\nconst styles = StyleSheet.create({\n  error: {\n    backgroundColor: 'firebrick',\n    color: 'white',\n  },\n});\n"]}